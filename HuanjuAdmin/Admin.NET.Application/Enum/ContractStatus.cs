﻿// MIT License
//
// Copyright (c) 2021-present <PERSON>ohua<PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Enum;

[Description("合同状态枚举")]
public enum ContractStatus
{
    /// <summary>
    /// 洽谈
    /// </summary>
    [Description("洽谈")]
    Negotiation = 0,

    /// <summary>
    /// 待审核
    /// </summary>
    [Description("待审核")]
    Audit = 1,

    /// <summary>
    /// 已签约
    /// </summary>
    [Description("已签约")]
    Signed = 2,

    /// <summary>
    /// 履约中
    /// </summary>
    [Description("履约中")]
    Performance= 3,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Completed = 4,

    /// <summary>
    /// 中止
    /// </summary>
    [Description("中止")]
    Discontinue = 5,
}
