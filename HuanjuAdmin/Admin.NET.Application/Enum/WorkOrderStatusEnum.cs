namespace Admin.NET.Application;

/// <summary>
/// 工单状态枚举
/// </summary>
[Description("工单状态枚举")]
public enum WorkOrderStatusEnum
{
    /// <summary>
    /// 待分配
    /// </summary>
    [Description("待分配")]
    Create = 0,

    /// <summary>
    /// 处理中
    /// </summary>
    [Description("处理中")]
    NotApproved = 1,

    /// <summary>
    /// 待确认
    /// </summary>
    [Description("待确认")]
    Approved = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Completed = 3,
}