namespace Admin.NET.Application;

/// <summary>
/// 入库状态枚举
/// </summary>
[Description("入库状态枚举")]
public enum RcvStatusEnum
{
    /// <summary>
    /// 未入库
    /// </summary>
    [Description("未入库")]
    NotReceived = 0,

    /// <summary>
    /// 待入库
    /// </summary>
    [Description("待入库")]
    Tobestored = 1,

    /// <summary>
    /// 部分入库
    /// </summary>
    [Description("部分入库")]
    PartiallyReceived = 2,

    /// <summary>
    /// 已入库
    /// </summary>
    [Description("已入库")]
    Received = 3,

    /// <summary>
    /// 已中止
    /// </summary>
    [Description("中止入库")]
    Suspend = 4,

}