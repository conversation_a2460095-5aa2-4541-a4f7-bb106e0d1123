﻿// MIT License
//
// Copyright (c) 2021-present <PERSON><PERSON><PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Enum;
[Description("销售状态枚举")]
public enum SalesStatusEnmu
{
    /// <summary>
    /// 确认
    /// </summary>
    [Description("确认")]
    Confirm = 0,

    /// <summary>
    /// 待确认
    /// </summary>
    [Description("待确认")]
    InConfirm = 1,
}
