namespace Admin.NET.Application;

/// <summary>
/// 通用单据状态枚举
/// </summary>
[Description("通用单据状态枚举")]
public enum OrderStatusEnum
{
    /// <summary>
    /// 创建
    /// </summary>
    [Description("创建")]
    Create = 0,

    /// <summary>
    /// 待审核
    /// </summary>
    [Description("待审核")]
    NotApproved = 1,

    /// <summary>
    /// 已审核
    /// </summary>
    [Description("已审核")]
    Approved = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Completed = 3,

    /// <summary>
    /// 已退回
    /// </summary>
    [Description("已退回")]
    Refunded = -1,
}