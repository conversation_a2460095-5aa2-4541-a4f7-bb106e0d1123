﻿// MIT License
//
// Copyright (c) 2021-present <PERSON><PERSON><PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Enum;
[Description("销售进度枚举")]
public enum SalesScheduleEnmu
{
    /// <summary>
    /// 完成
    /// </summary>
    [Description("完成")]
    Complete = 0,

    /// <summary>
    /// 未完成
    /// </summary>
    [Description("未完成")]
    Incomplete = 1,
}
