namespace Admin.NET.Application;

/// <summary>
/// 审批状态枚举
/// </summary>
[Description("审批状态枚举")]
public enum ApproveStatusEnum
{
    /// <summary>
    /// 待审批
    /// </summary>
    [Description("待审批")]
    NotApproved = 0,

    /// <summary>
    /// 流转中
    /// </summary>
    [Description("流转中")]
    InProgress = 1,

    /// <summary>
    /// 已完成
    /// </summary>
    [Description("已完成")]
    Completed = 2,

    /// <summary>
    /// 已拒绝
    /// </summary>
    [Description("已拒绝")]
    Refunded = -1,

}