﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品入库表
    /// </summary>
    [SugarTable("warehouseinrecord", "商品入库表")]
    [Tenant("1300000000001")]
    public class WarehouseInrecord : EntityTenant
    {
        /// <summary>
        /// 入库单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "入库单号", Length = 16)]
        public string OrderNumber { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID")]
        public long? SupplierId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier supplier { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [SugarColumn(ColumnDescription = "采购单号", Length = 16)]
        public string? PurchaseNumber { get; set; }
        /// <summary>
        /// 入库类型
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "入库类型")]
        public RcvTypeEnum InhouseType { get; set; }
        /// <summary>
        /// 入库状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "入库状态")]
        public RcvStatusEnum InhouseStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 100)]
        public string? Remark { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库ID", Length = 20)]
        public long? Warehouseid { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        [SugarColumn(ColumnDescription = "商品信息", Length = 1000)]
        public string? GoodsInfo { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [SugarColumn(ColumnDescription = "总金额")]
        public decimal? TotalAmt { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        [SugarColumn(ColumnDescription = "优惠金额")]
        public decimal? DiscountAmt { get; set; }

        /// <summary>
        /// 实际金额
        /// </summary>
        [SugarColumn(ColumnDescription = "实际金额")]
        public decimal? ActualAmt { get; set; }
    }
}