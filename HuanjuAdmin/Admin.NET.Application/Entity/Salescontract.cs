﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("salescontract","")]
    [Tenant("1300000000001")]
    public class Salescontract  : EntityTenant
    {
        /// <summary>
        /// 销售人
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "销售人", Length = 50)]
        public string Salesperson { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "销售单号", Length = 50)]
        public string SalesOrder { get; set; }
        /// <summary>
        /// 合同编码
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "合同编码", Length = 50)]
        public string ContractCode { get; set; }
        /// <summary>
        /// 合同状态
        /// </summary>
        //[Required]
        [SugarColumn(ColumnDescription = "合同状态")]
        public int ContractStatus { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "总金额")]
        public decimal TotalAmt { get; set; }
        /// <summary>
        /// 优惠金额
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "优惠金额")]
        public decimal DiscountAmt { get; set; }
        /// <summary>
        /// 合同金额
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "合同金额")]
        public decimal ContractAmount { get; set; }
        /// <summary>
        /// 签订时间
        /// </summary>
        [SugarColumn(ColumnDescription = "签订时间")]
        public DateTime? SigningTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(ColumnDescription = "结束时间")]
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        [SugarColumn(ColumnDescription = "商品信息", Length = 1000)]
        public string? GoodsInfo { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnDescription = "客户名称", Length = 50)]
        public string? CustomerName { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnDescription = "客户ID")]
        public long CustomerId { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnDescription = "联系人", Length = 20)]
        public string? Contacts { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(ColumnDescription = "电话", Length = 50)]
        public string? Tel { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址", Length = 200)]
        public string? Address { get; set; }
        /// <summary>
        /// 已收金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已收金额")]
        public decimal? AmountReceived { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已开票金额")]
        public decimal? InvoicedAmount { get; set; }
        /// <summary>
        /// 是否缺货
        /// </summary>
        [SugarColumn(ColumnDescription = "是否缺货")]
        public bool? OutOfStock { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? Notes { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        [SugarColumn(ColumnDescription = "附件", Length = 200)]
        public string? Annex { get; set; }
    }
}