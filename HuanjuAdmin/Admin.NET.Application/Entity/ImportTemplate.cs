﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("ImportTemplate","")]
    [Tenant("1300000000001")]
    public class ImportTemplate  : EntityBase
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [SugarColumn(ColumnDescription = "模板名称", Length = 64)]
        public string? Name { get; set; }
        /// <summary>
        /// 版本
        /// </summary>
        [SugarColumn(ColumnDescription = "版本")]
        public int? Version { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        [SugarColumn(ColumnDescription = "文件路径", Length = 64)]
        public string? FilePath { get; set; }
    }
}