﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品库存表
    /// </summary>
    [SugarTable("Warehouse_store", "")]
    [Tenant("1300000000001")]
    public class WarehouseStore : EntityTenant
    {
        /// <summary>
        /// 序号
        /// </summary>
        [SugarColumn(ColumnDescription = "序号")]
        public int? Number { get; set; }
        /// <summary>
        /// 仓库编号
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库编号")]
        public long? WarehouseId { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库", Length = 100)]
        public string? Warehouse { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
        public Warehouse Warehouses { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "库存数量")]
        public int? Quantity { get; set; }
        /// <summary>
        /// 销售占用数
        /// </summary>
        [SugarColumn(ColumnDescription = "销售占用数")]
        public int? SalesOccupancy { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnDescription = "批次号", Length = 50)]
        public string batchnumber { get; set; }
        /// <summary>
        /// 可销售数量
        /// </summary>
        [SugarColumn(ColumnDescription = "可销售数量")]
        public int? Marketable { get; set; }
        /// <summary>
        /// 可配数
        /// </summary>
        [SugarColumn(ColumnDescription = "可配数")]
        public int? Compatible { get; set; }
        /// <summary>
        /// 待出库数
        /// </summary>
        [SugarColumn(ColumnDescription = "待出库数")]
        public int? ShippedoutNum { get; set; }
        /// <summary>
        /// 在途数
        /// </summary>
        [SugarColumn(ColumnDescription = "在途数")]
        public int? IntransitNum { get; set; }
        /// <summary>
        /// 是否缺货
        /// </summary>
        [SugarColumn(ColumnDescription = "是否缺货")]
        public bool? StockOrNot { get; set; }
        /// <summary>
        /// 保质期(-1-不设置,0-正常,1-临期,2-过期)
        /// </summary>
        [SugarColumn(ColumnDescription = "保质期")]
        public int? ExpiredWarning { get; set; }
        /// <summary>
        /// 库存(-1-不设置,0-正常,1-库存不足,2-库存过剩) 
        /// </summary>
        [SugarColumn(ColumnDescription = "库存")]
        public int? StockWarning { get; set; }
        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID", Length = 50)]
        public long TradeID { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(TradeID))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 20)]
        public long? Unit { get; set; }


        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Unit))]
        public WarehouseGoodsUnit WarehouseGoodsUnit { get; set; }
        /// <summary>
        /// 是否保质期
        /// </summary>
        [SugarColumn(ColumnDescription = "是否保质期")]
        public bool? IsWarranty { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商", Length = 50)]
        public string? Supplier { get; set; }
        ///// <summary>
        ///// 商品编码
        ///// </summary>
        //[SugarColumn(ColumnDescription = "商品编码", Length = 100)]
        //public string? ProductCode { get; set; }
        ///// <summary>
        ///// 商品条码
        ///// </summary>
        //[SugarColumn(ColumnDescription = "商品条码", Length = 200)]
        //public string? BarCode { get; set; }
        /// <summary>
        /// 是否唯一码
        /// </summary>
        [SugarColumn(ColumnDescription = "是否唯一码")]
        public bool? IsUniqueCode { get; set; }
        /// <summary>
        /// 保质期
        /// </summary>
        [SugarColumn(ColumnDescription = "保质期")]
        public int? Warranty { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        [SugarColumn(ColumnDescription = "生产日期")]
        public DateTime? ProduceTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? Notes { get; set; }
        /// <summary>
        /// 当前成本
        /// </summary>
        [SugarColumn(ColumnDescription = "当前成本")]
        public decimal CurrentCost { get; set; }

        /// <summary>
        /// 当前采购单价
        /// </summary>
        [SugarColumn(ColumnDescription = "当前采购单价")]
        public decimal PurchaseUnitPrice { get; set; }


        /// <summary>
        /// 最高安全库存数
        /// </summary>
        [SugarColumn(ColumnDescription = "最高安全库存数")]
        public int SafetyStockTallNum { get; set; }

        /// <summary>
        /// 最高安全库存数
        /// </summary>
        [SugarColumn(ColumnDescription = "最高安全库存数")]
        public int SafetyStockLowNum { get; set; }


        /// <summary>
        /// 良品数量
        /// </summary>
        [SugarColumn(ColumnDescription = "良品数量")]
        public int GoodProduct { get; set; }


        /// <summary>
        /// 次品数量
        /// </summary>
        [SugarColumn(ColumnDescription = "次品数量")]
        public int Reject { get; set; }


    }
}