﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("warerevenue","")]
    [Tenant("1300000000001")]
    public class warerevenue  : EntityBase
    {
        /// <summary>
        /// 收支时间
        /// </summary>
        [SugarColumn(ColumnDescription = "收支时间")]
        public DateTime? revenueTime { get; set; }
        /// <summary>
        /// 来往单位ID
        /// </summary>
        [SugarColumn(ColumnDescription = "来往单位ID")]
        public long? CompanyID { get; set; }
        /// <summary>
        /// 来往单位
        /// </summary>
        [SugarColumn(ColumnDescription = "来往单位")]
        public string contactunits { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        [SugarColumn(ColumnDescription = "关联单号")]
        public string ordernumber { get; set; }
        /// <summary>
        /// 收支科目
        /// </summary>
        [SugarColumn(ColumnDescription = "收支科目")]
        public string? subject { get; set; }
        /// <summary>
        /// 二级科目
        /// </summary>
        [SugarColumn(ColumnDescription = "二级科目")]
        public string? levelsubject { get; set; }
        /// <summary>
        /// 收入金额
        /// </summary>
        [SugarColumn(ColumnDescription = "收入金额")]
        public decimal? Incomeamount { get; set; }
        /// <summary>
        /// 支出金额
        /// </summary>
        [SugarColumn(ColumnDescription = "支出金额")]
        public decimal? expenditureamount { get; set; }
        /// <summary>
        /// 经办人
        /// </summary>
        [SugarColumn(ColumnDescription = "经办人")]
        public long? handledby { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? notes { get; set; }
        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id")]
        public long? TenantId { get; set; }
        /// <summary>
        /// 付款类型 1收入，2支出
        /// </summary>
        [SugarColumn(ColumnDescription = "付款类型")]
        public int? revenueType { get; set; }
        /// <summary>
        /// 交易账户
        /// </summary>
        [SugarColumn(ColumnDescription = "交易账户")]
        public long? Trading { get; set; }
    }
}