﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 加工单配置
    /// </summary>
    [SugarTable("processorderscheme","")]
    [Tenant("1300000000001")]
    public class ProcessOrderScheme  : EntityTenant
    {
        /// <summary>
        /// 方案编号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "方案编号", Length = 50)]
        public string SchemeNo { get; set; }
        /// <summary>
        /// 方案名称
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "方案名称", Length = 255)]
        public string? SchemeName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(ColumnDescription = "状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(CreateUserId))]
        public SysUser CreateUser { get; set; }
        
/// <summary>
        /// 原料仓库
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "原料仓库")]
        public long? MaterialWarehouseId { get; set; }
        /// <summary>
        /// 成品仓库
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "成品仓库")]
        public long? ProduceWarehouseId { get; set; }

        /// <summary>
        /// 原料仓库
        /// </summary>
        [Navigate(NavigateType.OneToOne,nameof(MaterialWarehouseId))]
        public Warehouse MaterialWarehouse { get; set; }

        /// <summary>
        /// 成品仓库
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProduceWarehouseId))]
        public Warehouse ProduceWarehouse { get; set; }

        /// <summary>
        /// 加工单配置原料列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ProcessOrderSchemeMaterial.ProcessOrderSchemeId), nameof(ProcessOrderScheme.Id))]
        public List<ProcessOrderSchemeMaterial>? MaterialList { get; set; }
        /// <summary>
        /// 加工单配置产出列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ProcessOrderSchemeProduce.ProcessOrderSchemeId), nameof(ProcessOrderScheme.Id))]
        public List<ProcessOrderSchemeProduce>? ProduceList { get; set; }
    }
}