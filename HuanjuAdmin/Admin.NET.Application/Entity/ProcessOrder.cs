﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("processorder","")]
    [Tenant("1300000000001")]
    public class ProcessOrder  : EntityTenant
    {
        /// <summary>
        /// 加工单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "加工单号", Length = 50)]
        public string OrderNo { get; set; }
        /// <summary>
        /// 加工单方案Id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "加工单方案Id")]
        public long SchemeId { get; set; }
        /// <summary>
        /// 原料仓库
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "原料仓库")]
        public long? MaterialWarehouseId { get; set; }
        /// <summary>
        /// 成品仓库
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "成品仓库")]
        public long? ProduceWarehouseId { get; set; }

        /// <summary>
        /// 加工单方案
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(SchemeId))]
        public ProcessOrderScheme Scheme { get; set; }

        /// <summary>
        /// 原料仓库
        /// </summary>
        [Navigate(NavigateType.OneToOne,nameof(MaterialWarehouseId))]
        public Warehouse MaterialWarehouse { get; set; }

        /// <summary>
        /// 成品仓库
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProduceWarehouseId))]
        public Warehouse ProduceWarehouse { get; set; }

        /// <summary>
        /// 原料列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ProcessOrderMaterial.ProcessOrderId), nameof(ProcessOrderMaterial.Id))]
        public List<ProcessOrderMaterial> MaterialList { get; set; }

        /// <summary>
        /// 成品列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ProcessOrderProduce.ProcessOrderId), nameof(ProcessOrderProduce.Id))]
        public List<ProcessOrderProduce> ProduceList { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(CreateUserId))]
        public SysUser CreateUser { get; set; }

    }
}