﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商城商品表
    /// </summary>
    [SugarTable("eshopgoods","商城商品表")]
    [Tenant("1300000000001")]
    public class EshopGoods  : EntityBase
    {
        /// <summary>
        /// 标题
        /// </summary>
        [SugarColumn(ColumnDescription = "标题", Length = 200)]
        public string? Title { get; set; }
        /// <summary>
        /// 详情
        /// </summary>
        [SugarColumn(ColumnDescription = "详情", Length = 0)]
        public string? Detail { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "排序")]
        public int OrderNo { get; set; }
    }
}