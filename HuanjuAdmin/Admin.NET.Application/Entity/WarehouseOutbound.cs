﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("WarehouseOutbound","")]
    [Tenant("1300000000001")]
    public class WarehouseOutbound  : EntityBase
    {
        /// <summary>
        /// 出库单号
        /// </summary>
        [SugarColumn(ColumnDescription = "出库单号", Length = 20)]
        public string? OutboundId { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库")]
        public long? WarehouseId { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称", Length = 50)]
        public string? Tradename { get; set; }
        /// <summary>
        /// 商品条码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品条码", Length = 20)]
        public string? Barcode { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品编码", Length = 20)]
        public string? Productcode { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(ColumnDescription = "品牌", Length = 20)]
        public string? Brand { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        [SugarColumn(ColumnDescription = "规格", Length = 50)]
        public string? Specifications { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 20)]
        public string? Unit { get; set; }
        /// <summary>
        /// 是否缺货
        /// </summary>
        [SugarColumn(ColumnDescription = "是否缺货")]
        public int? Vacancy { get; set; }
        /// <summary>
        /// 出库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "出库数量")]
        public long? Outboundquantity { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnDescription = "客户名称", Length = 50)]
        public string? CustomerName { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnDescription = "联系人", Length = 20)]
        public string? Contacts { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(ColumnDescription = "电话", Length = 20)]
        public string? Phone { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址", Length = 100)]
        public string? Address { get; set; }
        /// <summary>
        /// 出库类型
        /// </summary>
        [SugarColumn(ColumnDescription = "出库类型", Length = 10)]
        public string? Outboundtype { get; set; }
        /// <summary>
        /// 出库时间
        /// </summary>
        [SugarColumn(ColumnDescription = "出库时间")]
        public DateTime? Deliverytime { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>
        [SugarColumn(ColumnDescription = "审核状态", Length = 10)]
        public string? Auditstatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? Notes { get; set; }
        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id")]
        public long? TenantId { get; set; }
    }
}