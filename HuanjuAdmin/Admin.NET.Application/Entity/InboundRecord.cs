﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 入库记录
    /// </summary>
    [SugarTable("inboundrecord", "入库记录")]
    [Tenant("1300000000001")]
    public class InboundRecord : EntityTenant
    {
        /// <summary>
        /// 入库明细Id
        /// </summary>
        [SugarColumn(ColumnDescription = "入库明细Id")]
        public long? WarehouseIncordMxId { get; set; }
        /// <summary>
        /// 入库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "入库数量")]
        public int? InBoundCount { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        [SugarColumn(ColumnDescription = "打印次数")]
        public int PrintCount { get; set; } = 0;
        /// <summary>
        /// 批次Id
        /// </summary>
        [SugarColumn(ColumnDescription = "批次Id")]
        public long? WarehouseBatchId { get; set; }
        /// <summary>
        /// 流水号
        /// </summary>
        [SugarColumn(ColumnDescription = "流水号")]
        public string OrderNum { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 是否良品
        /// </summary>
        [SugarColumn(ColumnDescription = "是否良品")]
        public bool? GoodProduct { get; set; }
        
        /// <summary>
        /// 红冲状态：0-正常记录，1-已被红冲，2-红冲记录
        /// </summary>
        [SugarColumn(ColumnDescription = "红冲状态")]
        public int RedInkStatus { get; set; } = 0;
        
        /// <summary>
        /// 原始记录ID（红冲记录关联的原始记录）
        /// </summary>
        [SugarColumn(ColumnDescription = "原始记录ID")]
        public long? OriginalRecordId { get; set; }
    }
}