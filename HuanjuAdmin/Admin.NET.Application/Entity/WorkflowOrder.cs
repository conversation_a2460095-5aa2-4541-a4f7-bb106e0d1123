﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;

namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 流程审批单表
    /// </summary>
    [SugarTable("workfloworder", "流程审批单表")]
    [Tenant("1300000000001")]
    public class WorkflowOrder : EntityBaseId
    {
        /// <summary>
        /// 申请人ID
        /// </summary>
        [SugarColumn(ColumnDescription = "申请人ID")]
        public long? UerId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(UerId))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 审批单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批单号", Length = 20)]
        public string ApprovalNumber { get; set; }
        /// <summary>
        /// 流程ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "流程ID")]
        public long WorkflowId { get; set; }
        /// <summary>
        /// 流程
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WorkflowId))]
        public WorkflowReview WorkflowReview { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "创建时间")]
        public DateTime? CreateTime { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(ColumnDescription = "状态")]
        public ApproveStatusEnum Status { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "部门ID")]
        public long OrgId { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(OrgId))]
        public SysOrg SysOrg { get; set; }
    }
}