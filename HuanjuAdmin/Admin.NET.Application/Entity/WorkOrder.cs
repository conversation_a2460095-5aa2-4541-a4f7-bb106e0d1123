﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 工单表
    /// </summary>
    [SugarTable("workorder","工单表")]
    [Tenant("1300000000001")]
    public class WorkOrder  : EntityTenant
    {
        /// <summary>
        /// 工单编号
        /// </summary>
        [SugarColumn(ColumnDescription = "工单编号", Length = 16)]
        public string? OrderNumber { get; set; }
        /// <summary>
        /// 处理人ID
        /// </summary>
        [SugarColumn(ColumnDescription = "处理人ID")]
        public long? Processor { get; set; }
        /// <summary>
        /// 处理人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Processor))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 处理时间
        /// </summary>
        [SugarColumn(ColumnDescription = "处理时间")]
        public DateTime? ProcessTime { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        [SugarColumn(ColumnDescription = "问题描述", Length = 0)]
        public string? Describe { get; set; }
        /// <summary>
        /// 进度
        /// </summary>
        [SugarColumn(ColumnDescription = "进度")]
        public WorkOrderStatusEnum Process { get; set; }
        /// <summary>
        /// 处理方式
        /// </summary>
        [SugarColumn(ColumnDescription = "处理方式")]
        public WorkOrderProcessEnum Management { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 200)]
        public string? Remark { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public long? CustomId { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(CustomId))]
        public Pubcustom Pubcustom { get; set; }
    }
}