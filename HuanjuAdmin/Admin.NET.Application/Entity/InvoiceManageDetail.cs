﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 发票信息明细表
    /// </summary>
    [SugarTable("invoicemanagedetail","发票信息明细表")]
    [Tenant("1300000000001")]
    public class InvoiceManageDetail  : EntityTenantId
    {
        /// <summary>
        /// 发票ID
        /// </summary>
        [SugarColumn(ColumnDescription = "发票ID")]
        public long? InvoiceManageId { get; set; }
        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID")]
        public long? GoodsId { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称", Length = 255)]
        public string? GoodsName { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        [SugarColumn(ColumnDescription = "规格", Length = 255)]
        public string? Specs { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 10)]
        public string? Unit { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(ColumnDescription = "数量")]
        public int? SalesNum { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDescription = "单价")]
        public decimal? Price { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        [SugarColumn(ColumnDescription = "金额")]
        public decimal? Amount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [SugarColumn(ColumnDescription = "税率")]
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [SugarColumn(ColumnDescription = "税额")]
        public decimal? Tax { get; set; }
        /// <summary>
        /// 含税标志
        /// </summary>
        [SugarColumn(ColumnDescription = "含税标志")]
        public int? TaxTag { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品编码", Length = 64)]
        public string? GoodsCode { get; set; }
    }
}