﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("wareinventorychanges","")]
    [Tenant("1300000000001")]
    public class wareinventorychanges  : EntityBase
    {
        /// <summary>
        /// 商品Id
        /// </summary>
        [SugarColumn(ColumnDescription = "商品Id")]
        public long? goodsId { get; set; }
        /// <summary>
        /// 期初结存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "期初结存数量")]
        public decimal? OpeningNum { get; set; }
        /// <summary>
        /// 期初结存金额
        /// </summary>
        [SugarColumn(ColumnDescription = "期初结存金额")]
        public decimal? OpeningMoney { get; set; }
        /// <summary>
        /// 本期出库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "本期出库数量")]
        public decimal? CurrentNum { get; set; }
        /// <summary>
        /// 本期出库金额
        /// </summary>
        [SugarColumn(ColumnDescription = "本期出库金额")]
        public decimal? CurrentMoney { get; set; }
        /// <summary>
        /// 本期结存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "本期结存数量")]
        public decimal? BalanceNum { get; set; }
        /// <summary>
        /// 本期结存金额
        /// </summary>
        [SugarColumn(ColumnDescription = "本期结存金额")]
        public decimal? BalanceMoney { get; set; }
        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id")]
        public long? TenantId { get; set; }
    }
}