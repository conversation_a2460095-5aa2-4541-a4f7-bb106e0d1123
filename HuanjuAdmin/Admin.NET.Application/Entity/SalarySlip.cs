﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 工资条
    /// </summary>
    [SugarTable("salaryslip","工资条")]
    [Tenant("1300000000001")]
    public class SalarySlip  : EntityTenant
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "用户ID")]
        public long UserID { get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(UserID))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        [SugarColumn(ColumnDescription = "月份")]
        public DateTime SlipMonth { get; set; }
        /// <summary>
        /// 应勤天数
        /// </summary>
        [SugarColumn(ColumnDescription = "应勤天数")]
        public decimal? ShouldDays { get; set; }
        /// <summary>
        /// 出勤天数
        /// </summary>
        [SugarColumn(ColumnDescription = "出勤天数")]
        public decimal? WorkDays { get; set; }
        /// <summary>
        /// 应发工资
        /// </summary>
        [SugarColumn(ColumnDescription = "应发工资")]
        public decimal? ShouldMoney { get; set; }
        /// <summary>
        /// 绩效等级
        /// </summary>
        [SugarColumn(ColumnDescription = "绩效等级", Length = 10)]
        public string? Grading { get; set; }
        /// <summary>
        /// 绩效
        /// </summary>
        [SugarColumn(ColumnDescription = "绩效")]
        public decimal? KpiMoney { get; set; }
        /// <summary>
        /// 提成基数
        /// </summary>
        [SugarColumn(ColumnDescription = "提成基数")]
        public decimal? CommissionBase { get; set; }
        /// <summary>
        /// 提成系数
        /// </summary>
        [SugarColumn(ColumnDescription = "提成系数")]
        public decimal? CommissionRatio { get; set; }
        /// <summary>
        /// 提成
        /// </summary>
        [SugarColumn(ColumnDescription = "提成")]
        public decimal? Commission { get; set; }
        /// <summary>
        /// 迟到早退
        /// </summary>
        [SugarColumn(ColumnDescription = "迟到早退")]
        public int? WorkLateLeveEarly { get; set; }
        /// <summary>
        /// 考勤扣除
        /// </summary>
        [SugarColumn(ColumnDescription = "考勤扣除")]
        public decimal? AttendanceTakeOff { get; set; }
        /// <summary>
        /// 餐补
        /// </summary>
        [SugarColumn(ColumnDescription = "餐补")]
        public decimal? Meals { get; set; }
        /// <summary>
        /// 话费补贴
        /// </summary>
        [SugarColumn(ColumnDescription = "话费补贴")]
        public decimal? PhoneBill { get; set; }
        /// <summary>
        /// 交通补贴
        /// </summary>
        [SugarColumn(ColumnDescription = "交通补贴")]
        public decimal? TafficBill { get; set; }
        /// <summary>
        /// 住房补贴
        /// </summary>
        [SugarColumn(ColumnDescription = "住房补贴")]
        public decimal? HouseBill { get; set; }
        /// <summary>
        /// 全勤奖
        /// </summary>
        [SugarColumn(ColumnDescription = "全勤奖")]
        public decimal? Attendance { get; set; }
        /// <summary>
        /// 其它奖励
        /// </summary>
        [SugarColumn(ColumnDescription = "其它奖励")]
        public decimal? OrtheMoney { get; set; }
        /// <summary>
        /// 社保代扣
        /// </summary>
        [SugarColumn(ColumnDescription = "社保代扣")]
        public decimal? SocialInsurance { get; set; }
        /// <summary>
        /// 公积金代缴
        /// </summary>
        [SugarColumn(ColumnDescription = "公积金代缴")]
        public decimal? AccumulationFund { get; set; }
        /// <summary>
        /// 个税代缴
        /// </summary>
        [SugarColumn(ColumnDescription = "个税代缴")]
        public decimal? Tax { get; set; }
        /// <summary>
        /// 其它扣款
        /// </summary>
        [SugarColumn(ColumnDescription = "其它扣款")]
        public decimal? OrtherTakOff { get; set; }
        /// <summary>
        /// 实发
        /// </summary>
        [SugarColumn(ColumnDescription = "实发")]
        public decimal? ActualMoney { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 200)]
        public string? Remark { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "状态")]
        public SalarySlipStatusEnum Status { get; set; }
    }
}