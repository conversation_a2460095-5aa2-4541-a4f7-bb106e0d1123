﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("paymentorder","")]
    [Tenant("1300000000001")]
    public class Paymentorder  : EntityTenant
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        [SugarColumn(ColumnDescription = "付款单号", Length = 50)]
        public string? PaymentNo { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID", Length = 50)]
        public long? SupplierId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier supplier { get; set; }
        /// <summary>
        /// 合同编号
        /// </summary>
        [SugarColumn(ColumnDescription = "合同编号", Length = 50)]
        public string? ContractNum { get; set; }
        /// <summary>
        /// 单据金额
        /// </summary>
        [SugarColumn(ColumnDescription = "单据金额")]
        public decimal? DocumentAmount { get; set; }
        /// <summary>
        /// 已付金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已付金额")]
        public decimal? AmountPaid { get; set; }
        /// <summary>
        /// 已收票金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已收票金额")]
        public decimal? ReceivedAmount { get; set; }
        /// <summary>
        /// 成本类型
        /// </summary>
        [SugarColumn(ColumnDescription = "成本类型")]
        public int? CostType { get; set; }
        /// <summary>
        /// 支出名目
        /// </summary>
        [SugarColumn(ColumnDescription = "支出名目")]
        public string? ExpenditureCategory { get; set; }
        /// <summary>
        /// 二级科目
        /// </summary>
        [SugarColumn(ColumnDescription = "二级科目")]
        public string? SecondaryAccount { get; set; }
        /// <summary>
        /// 付款状态
        /// </summary>
        [SugarColumn(ColumnDescription = "付款状态")]
        public int? PaymentStatus { get; set; }
        /// <summary>
        /// 交易账户
        /// </summary>
        [SugarColumn(ColumnDescription = "交易账户")]
        public long? Trading { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        [SugarColumn(ColumnDescription = "发票状态")]
        public int? InvoiceStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 500)]
        public string? Notes { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        [SugarColumn(ColumnDescription = "采购单号")]
        public string PurchaseNumber { get; set; }

        /// <summary>
        /// 摘要
        /// </summary>
        [SugarColumn(ColumnDescription = "摘要", Length = 500)]
        public string? Abstract { get; set; }

        /// <summary>
        /// 发票单号
        /// </summary>
        [SugarColumn(ColumnDescription = "发票单号")]
        public string InvoiceNo { get; set; }
    }
}