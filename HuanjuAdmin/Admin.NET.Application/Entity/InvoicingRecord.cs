﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 开票记录
    /// </summary>
    [SugarTable("invoicingrecord","开票记录")]
    [Tenant("1300000000001")]
    public class InvoicingRecord  : EntityTenant
    {
        /// <summary>
        /// 单据号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "单据号", Length = 40)]
        public string DocumentNumber { get; set; }
        /// <summary>
        /// 收款单ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "收款单ID")]
        public long ReceiptID { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(ColumnDescription = "状态")]
        public int? Status { get; set; }
    }
}