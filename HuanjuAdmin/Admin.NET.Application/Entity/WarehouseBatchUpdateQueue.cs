﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品批次更新表
    /// </summary>
    [SugarTable("WarehouseBatchUpdateQueue", "")]
    [Tenant("1300000000001")]
    public class WarehouseBatchUpdateQueue : EntityBaseId
    {
        /// <summary>
        /// 批次商品ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "批次商品ID")]
        public long BatchId { get; set; }
        /// <summary>
        /// 目标时间
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "目标时间")]
        public DateTime TargetUpdateTime { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "状态")]
        public int Status { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "类型")]
        public int Type { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnDescription = "创建时间", IsOnlyIgnoreUpdate = true)]
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(ColumnDescription = "更新时间", IsOnlyIgnoreInsert = true)]
        public DateTime? UpdateTime { get; set; }
    }
}