﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 出入库记录
    /// </summary>
    [SugarTable("View_OutInBound", "出入库记录")]
    [Tenant("1300000000001")]
    public class OutInBoundRecord : EntityTenant
    {
        /// <summary>
        /// 类型
        /// </summary>
        [SugarColumn(ColumnDescription = "类型")]
        public string Type { get; set; }
        /// <summary>
        /// 上级ID
        /// </summary>
        [SugarColumn(ColumnDescription = "上级ID")]
        public long ParentId { get; set; }
        /// <summary>
        /// 流水号
        /// </summary>
        [SugarColumn(ColumnDescription = "流水号")]
        public string OrderNum { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(ColumnDescription = "数量")]
        public int OutInCount { get; set; }
        /// <summary>
        /// 打印次数
        /// </summary>
        [SugarColumn(ColumnDescription = "打印次数")]
        public int PrintCount { get; set; }
        /// <summary>
        /// 批次ID
        /// </summary>
        [SugarColumn(ColumnDescription = "批次ID")]
        public long WarehouseBatchId { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnDescription = "批次号")]
        public string BatchNumber { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        [SugarColumn(ColumnDescription = "单号")]
        public string OrderNumber { get; set; }
        /// <summary>
        /// 客户ID
        /// </summary>
        [SugarColumn(ColumnDescription = "客户ID")]
        public long? CustomId { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID")]
        public long? SupplierId { get; set; }
        /// <summary>
        /// 客户/供应商
        /// </summary>
        [SugarColumn(ColumnDescription = "客户/供应商")]
        public string Company { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        [SugarColumn(ColumnDescription = "联系方式")]
        public string TelPhone { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库ID")]
        public long? WarehouseId { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库")]
        public string WarehouseName { get; set; }
        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID")]
        public long? GoodsId { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称")]
        public string GoodsName { get; set; }
        /// <summary>
        /// 商品条码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品条码")]
        public string BarCode { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品编码")]
        public string Code { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(ColumnDescription = "品牌")]
        public string Brand { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        [SugarColumn(ColumnDescription = "规格")]
        public string Specs { get; set; }
        /// <summary>
        /// 商品单位ID
        /// </summary>
        [SugarColumn(ColumnDescription = "商品单位ID")]
        public long? UnitId { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位")]
        public string UnitName { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        [SugarColumn(ColumnDescription = "操作人")]
        public string CreateUserName { get; set; }

        /// <summary>
        /// 打印人
        /// </summary>
        [SugarColumn(ColumnDescription = "打印人")]
        public string UpdateUserName { get; set; }

        /// <summary>
        /// 是否良品
        /// </summary>
        [SugarColumn(ColumnDescription = "是否良品")]
        public bool? GoodProduct { get; set; }

        /// <summary>
        /// 红冲状态：0-正常记录，1-已被红冲，2-红冲记录
        /// </summary>
        [SugarColumn(ColumnDescription = "红冲状态")]
        public int RedInkStatus { get; set; } = 0;
    }
}