﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("warehouseuniquecode","")]
    [Tenant("1300000000001")]
    public class Warehouseuniquecode  : EntityTenant
    {

        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID", Length = 50)]
        public long? TradeID { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(TradeID))]
        public Warehousegoods Warehousegoods { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称", Length = 50)]
        public string? TradeName { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品编码", Length = 50)]
        public string? TradeCode { get; set; }
        /// <summary>
        /// 唯一码
        /// </summary>
        [SugarColumn(ColumnDescription = "唯一码", Length = 50)]
        public string? UniqueCode { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商", Length = 50)]
        public string? Supplier { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID", Length = 50)]
        public string? SupplierID { get; set; }
        /// <summary>
        /// 商品属性
        /// </summary>
        [SugarColumn(ColumnDescription = "商品属性", Length = 50)]
        public string? Attributes { get; set; }


    }
}