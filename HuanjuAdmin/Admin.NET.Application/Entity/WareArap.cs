﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("WareArap","")]
    [Tenant("1300000000001")]
    public class WareArap  : EntityBase
    {
        /// <summary>
        /// 待收金额
        /// </summary>
        [SugarColumn(ColumnDescription = "待收金额")]
        public decimal? PendingAmount { get; set; }
        /// <summary>
        /// 代付金额
        /// </summary>
        [SugarColumn(ColumnDescription = "代付金额")]
        public decimal? AmountPaid { get; set; }
        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id")]
        public long? TenantId { get; set; }
        /// <summary>
        /// 来往单位
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "来往单位")]
        public long Contactunits { get; set; }
        /// <summary>
        /// 类别
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "类别")]
        public int category { get; set; }
    }
}