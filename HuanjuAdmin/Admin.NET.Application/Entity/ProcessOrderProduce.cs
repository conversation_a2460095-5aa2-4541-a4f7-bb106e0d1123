﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("processorderproduce","")]
    [Tenant("1300000000001")]
    public class ProcessOrderProduce  : EntityBaseId
    {
        /// <summary>
        /// 加工单Id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "加工单Id")]
        public long ProcessOrderId { get; set; }
        /// <summary>
        /// 产品Id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "产品Id")]
        public long WarehousegoodsId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WarehousegoodsId))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 基准数量
        /// </summary>
        [SugarColumn(ColumnDescription = "基准数量")]
        public decimal? BaseQuantity { get; set; }
        /// <summary>
        /// 实际数量
        /// </summary>
        [SugarColumn(ColumnDescription = "实际数量")]
        public decimal? ActQuantity { get; set; }
        /// <summary>
        /// 成品单价
        /// </summary>
        [SugarColumn(ColumnDescription = "成品单价")]
        public decimal? UnitPrice { get; set; }
        /// <summary>
        /// 成品总价
        /// </summary>
        [SugarColumn(ColumnDescription = "成品总价")]
        public decimal? TotalPrice { get; set; }
    }
}