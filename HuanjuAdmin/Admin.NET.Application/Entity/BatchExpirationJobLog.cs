using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
using System;

namespace Admin.NET.Application.Entity;

/// <summary>
/// 批次商品保质期检查任务日志表
/// </summary>
[SugarTable("BatchExpirationJobLog", "批次商品保质期检查任务日志表")]
[Tenant("1300000000001")]
public class BatchExpirationJobLog : EntityBase
{
    /// <summary>
    /// 执行状态（0-失败，1-成功）
    /// </summary>
    [SugarColumn(ColumnDescription = "执行状态")]
    public int Status { get; set; }

    /// <summary>
    /// 处理数量
    /// </summary>
    [SugarColumn(ColumnDescription = "处理数量")]
    public int ProcessCount { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnDescription = "错误信息", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "结束时间")]
    public DateTime? EndTime { get; set; }
} 