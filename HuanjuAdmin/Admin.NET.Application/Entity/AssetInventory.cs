﻿using System;
using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 固资库存表
    /// </summary>
    [SugarTable("AssetInventory","固资库存表")]
    [Tenant("1300000000001")]
    public class AssetInventory  : EntityTenant
    {
        /// <summary>
        /// 名称
        /// </summary>
        [SugarColumn(ColumnDescription = "名称")]
        public string Name { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(ColumnDescription = "品牌")]
        public string? Brand { get; set; }
        /// <summary>
        /// 编码
        /// </summary>
        [SugarColumn(ColumnDescription = "编码")]
        public string? Code { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        [SugarColumn(ColumnDescription = "规格")]
        public string? Specs { get; set; }
        /// <summary>
        /// 总数量
        /// </summary>
        [SugarColumn(ColumnDescription = "总数量")]
        public int TotalCount { get; set; }
        /// <summary>
        /// 借用数量
        /// </summary>
        [SugarColumn(ColumnDescription = "借用数量")]
        public int BorrowCount { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "库存数量")]
        public int InventoryCount { get; set; }
        /// <summary>
        /// 初始总数量
        /// </summary>
        [SugarColumn(ColumnDescription = "总数量")]
        public int BaseTotalCount { get; set; }
        /// <summary>
        /// 初始借用数量
        /// </summary>
        [SugarColumn(ColumnDescription = "借用数量")]
        public int BaseBorrowCount { get; set; }
        /// <summary>
        /// 初始库存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "库存数量")]
        public int BaseInventoryCount { get; set; }
        /// <summary>
        /// 需要归还
        /// </summary>
        [SugarColumn(ColumnDescription = "需要归还")]
        public bool IsNdReturn { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string? Remark { get; set; }
    }
}