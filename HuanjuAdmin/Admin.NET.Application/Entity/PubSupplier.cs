﻿using System;
using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 供应商信息表
    /// </summary>
    [SugarTable("PubSupplier","供应商信息表")]
    [Tenant("1300000000001")]
    public class PubSupplier  : EntityTenant
    {
        /// <summary>
        /// 供应商名称
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商名称")]
        public string Name { get; set; }
        /// <summary>
        /// 供应商类型
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商类型")]
        public string? Type { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnDescription = "联系人")]
        public string Contacts { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(ColumnDescription = "电话")]
        public string Phone { get; set; }
        /// <summary>
        /// 公用标志
        /// </summary>
        [SugarColumn(ColumnDescription = "公用标志")]
        public bool IsCommunal { get; set; }
        /// <summary>
        /// 开户行
        /// </summary>
        [SugarColumn(ColumnDescription = "开户行")]
        public string? BankName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string? Remark { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        [SugarColumn(ColumnDescription = "银行账号")]
        public string? BankCode { get; set; }
        /// <summary>
        /// 税务登记号
        /// </summary>
        [SugarColumn(ColumnDescription = "税务登记号")]
        public string? TaxId { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址")]
        public string? Address { get; set; }

        /// <summary>
        /// 机构Id
        /// </summary>
        [SugarColumn(ColumnDescription = "机构Id")]
        public long OrgId { get; set; }

        /// <summary>
        /// 机构
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(OrgId))]
        public SysOrg SysOrg { get; set; }
    }
}