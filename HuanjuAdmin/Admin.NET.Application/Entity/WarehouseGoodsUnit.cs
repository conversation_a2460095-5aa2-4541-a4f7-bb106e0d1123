﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品单位表
    /// </summary>
    [SugarTable("warehousegoodsunit","商品单位表")]
    [Tenant("1300000000001")]
    public class WarehouseGoodsUnit  : EntityTenant
    {
        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "单位", Length = 255)]
        public string Name { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [SugarColumn(ColumnDescription = "状态")]
        public bool Status { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 255)]
        public string? Remark { get; set; }
    }
}