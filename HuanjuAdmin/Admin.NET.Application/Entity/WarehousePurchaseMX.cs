﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;

namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品采购明细表
    /// </summary>
    [SugarTable("WarehousePurchaseMX","")]
    [Tenant("1300000000001")]
    public class WarehousePurchaseMX  : EntityTenant
    {
        /// <summary>
        /// 采购单ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "采购单ID")]
        public long PurchaseId { get; set; }
        /// <summary>
        /// 商品ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "商品ID")]
        public long GoodsId { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(GoodsId))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 10)]
        public long? Unit { get; set; }

        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Unit))]
        public WarehouseGoodsUnit warehousegoodsunit { get; set; }
        /// <summary>
        /// 采购数量
        /// </summary>
        [SugarColumn(ColumnDescription = "采购数量")]
        public int? PuchQty { get; set; }
        /// <summary>
        /// 采购单价
        /// </summary>
        [SugarColumn(ColumnDescription = "采购单价")]
        public decimal? PuchPrice { get; set; }
        /// <summary>
        /// 采购金额
        /// </summary>
        [SugarColumn(ColumnDescription = "采购金额")]
        public decimal? PuchAmt { get; set; }
        /// <summary>
        /// 入库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "入库数量")]
        public int? RcvQty { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID")]
        public long? SupplierId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier PubSupplier { get; set; }
    }
}