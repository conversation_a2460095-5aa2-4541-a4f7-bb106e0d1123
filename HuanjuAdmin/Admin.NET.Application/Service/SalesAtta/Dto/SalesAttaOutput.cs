﻿namespace Admin.NET.Application;

    /// <summary>
    /// 合同附件输出参数
    /// </summary>
    public class SalesAttaOutput
    {
       /// <summary>
       /// 附件地址
       /// </summary>
        public string? Address { get; set; }
        public SysFile AddressAttachment { get; set; }
    
       /// <summary>
       /// 销售单号
       /// </summary>
       public string SalesID { get; set; }
    
       /// <summary>
       /// 附件名称
       /// </summary>
       public string? AttaName { get; set; }

        public long Id { get; set; }

}
 

