﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 合同附件基础输入参数
    /// </summary>
    public class SalesAttaBaseInput
    {
        /// <summary>
        /// 附件地址
        /// </summary>
        public virtual string? Address { get; set; }
        
        /// <summary>
        /// 销售单号
        /// </summary>
        public virtual string SalesID { get; set; }
        
        /// <summary>
        /// 附件名称
        /// </summary>
        public virtual string? AttaName { get; set; }
        
    }

    /// <summary>
    /// 合同附件分页查询输入参数
    /// </summary>
    public class SalesAttaInput : BasePageInput
    {
    /// <summary>
    /// 销售单号
    /// </summary>
    public string salesID { get; set; }
}

    /// <summary>
    /// 合同附件增加输入参数
    /// </summary>
    public class AddSalesAttaInput : SalesAttaBaseInput
    {
    }

    /// <summary>
    /// 合同附件删除输入参数
    /// </summary>
    public class DeleteSalesAttaInput : BaseIdInput
    {
    }

    /// <summary>
    /// 合同附件更新输入参数
    /// </summary>
    public class UpdateSalesAttaInput : SalesAttaBaseInput
    {
    }

    /// <summary>
    /// 合同附件主键查询输入参数
    /// </summary>
    public class QueryByIdSalesAttaInput : DeleteSalesAttaInput
    {

    }
