﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;
/// <summary>
/// 合同附件服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SalesAttaService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SalesAtta> _rep;
    public SalesAttaService(SqlSugarRepository<SalesAtta> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询合同附件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SalesAttaOutput>> Page(SalesAttaInput input)
    {
        var query= _rep.AsQueryable()
           .WhereIF(!string.IsNullOrWhiteSpace(input.salesID), u => u.SalesID== input.salesID)
                    .Select(u=> new SalesAttaOutput{
                        Address = u.Address, 
                        SalesID = u.SalesID, 
                        AttaName = u.AttaName, 
                        Id=u.Id
                    })
                    //.Mapper(c => c.AddressAttachment, c => c.Address)
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加合同附件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSalesAttaInput input)
    {
        var entity = input.Adapt<SalesAtta>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除合同附件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSalesAttaInput input)
    {
        var entity = input.Adapt<SalesAtta>();
      var a=  await _rep.DeleteAsync(entity);   //假删除
        var b = a;
    }

    /// <summary>
    /// 更新合同附件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateSalesAttaInput input)
    {
        var entity = input.Adapt<SalesAtta>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取合同附件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<SalesAtta> Get([FromQuery] QueryByIdSalesAttaInput input)
    //{
    //}

    /// <summary>
    /// 获取合同附件列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SalesAttaOutput>> List([FromQuery] SalesAttaInput input)
    {
        return await _rep.AsQueryable().Select<SalesAttaOutput>().ToListAsync();
    }


    /// <summary>
    /// 上传附件地址
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UploadAddress"), HttpPost]
    public async Task<FileOutput> UploadAddress([Required] IFormFile file)
    {
            var service = App.GetService<SysFileService>();
            return await service.UploadFile(file, "upload/Address"); 
    }


    /// <summary>
    /// 上传
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UploadQN"), HttpPost]
    public async Task<string> UploadQN([Required] IFormFileCollection file)
    {
        return await FileUtil.UploadPicToQiniu(file, "img", "");
    }


}

