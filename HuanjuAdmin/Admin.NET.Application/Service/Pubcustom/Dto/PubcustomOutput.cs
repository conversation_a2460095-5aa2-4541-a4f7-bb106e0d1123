﻿namespace Admin.NET.Application;

    /// <summary>
    /// 客户信息输出参数
    /// </summary>
    public class PubcustomOutput
    {
       /// <summary>
       /// Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 客户名称
       /// </summary>
       public string Name { get; set; }
    
       /// <summary>
       /// 客户行业
       /// </summary>
       public string? Type { get; set; }
    
       /// <summary>
       /// 联系人
       /// </summary>
       public string Contacts { get; set; }
    
       /// <summary>
       /// 电话
       /// </summary>
       public string Phone { get; set; }
    
       /// <summary>
       /// 公用标志
       /// </summary>
       public bool IsCommunal { get; set; }
    
       /// <summary>
       /// 开户行
       /// </summary>
       public string? BankName { get; set; }
    
       /// <summary>
       /// 备注
       /// </summary>
       public string? Remark { get; set; }
    
       /// <summary>
       /// 银行账号
       /// </summary>
       public string? BankCode { get; set; }
    
       /// <summary>
       /// 税务登记号
       /// </summary>
       public string? TaxId { get; set; }
    
       /// <summary>
       /// 地址
       /// </summary>
       public string? Address { get; set; }
    
       /// <summary>
       /// 机构ID
       /// </summary>
       public long OrgId { get; set; }
    
    }
 

