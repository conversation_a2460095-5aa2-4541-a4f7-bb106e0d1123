﻿namespace Admin.NET.Application;

    /// <summary>
    /// 唯一码库输出参数
    /// </summary>
    public class WarehouseuniquecodeOutput
    {
       /// <summary>
       /// 序号
       /// </summary>
       public long Order { get; set; }
    
       /// <summary>
       /// 商品名称
       /// </summary>
       public string? TradeName { get; set; }
    
       /// <summary>
       /// 商品编码
       /// </summary>
       public string? TradeCode { get; set; }
    
       /// <summary>
       /// 唯一码
       /// </summary>
       public string? UniqueCode { get; set; }
    
       /// <summary>
       /// 供应商
       /// </summary>
       public string? Supplier { get; set; }
    
       /// <summary>
       /// 供应商ID
       /// </summary>
       public string? SupplierID { get; set; }
    
       /// <summary>
       /// 商品属性
       /// </summary>
       public string? Attributes { get; set; }
    
    }
 

