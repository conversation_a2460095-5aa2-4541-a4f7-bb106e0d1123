﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;

namespace Admin.NET.Application;
/// <summary>
/// 固资库存服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class AssetInventoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<AssetInventory> _rep;
    private readonly UserManager _userManager;
    public AssetInventoryService(SqlSugarRepository<AssetInventory> rep, UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询固资库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<AssetInventoryOutput>> Page(AssetInventoryInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))

                    .Select<AssetInventoryOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加固资库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddAssetInventoryInput input)
    {
        var entity = input.Adapt<AssetInventory>();
        entity.BaseBorrowCount = input.BorrowCount;
        entity.BaseInventoryCount = input.InventoryCount;
        entity.BaseTotalCount = input.TotalCount;
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除固资库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteAssetInventoryInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新固资库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateAssetInventoryInput input)
    {
        var entity = input.Adapt<AssetInventory>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取固资库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<AssetInventory> Get([FromQuery] QueryByIdAssetInventoryInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取固资库存列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<AssetInventoryOutput>> List([FromQuery] AssetInventoryInput input)
    {
        return await _rep.AsQueryable().Select<AssetInventoryOutput>().ToListAsync();
    }





}

