﻿using Admin.NET.Application.Entity;
using Admin.NET.Application.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service;
public class Func_UpdateStatus
{
    private readonly SqlSugarRepository<SalesperFormanceplan> _rep;
    private readonly SqlSugarRepository<Salescontract> _reps;
    public Func_UpdateStatus(SqlSugarRepository<SalesperFormanceplan> rep, SqlSugarRepository<Salescontract> reps)
    {
        _rep = rep;
        _reps = reps;
    }

    public async Task UpdateStatus(UpdateSalesperFormanceplanInput input)
    {
        var entity = input.Adapt<SalesperFormanceplan>();
        //获取销售合约
        var query = _reps.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder).Select<Salescontract>().FirstAsync().Result;
        //获取所有履约计划
        var list = _rep.AsQueryable().Where(u => u.SalesOrder == "" + query.Id && u.Type != SalesTypeEnmu.Invoicing).ToList();
        //履约计划有一项是不是未完成或者中止，合约为完成状态改为履约中
        if (list.Count > 0)
        {
            if (list.All(u => u.Plan == 0) && query.ContractStatus == 3)
            {
                query.ContractStatus = 2;
                await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
            if (list.Any(u => u.Plan > 0 && u.Plan < 3) && query.ContractStatus == 2)
            {
                query.ContractStatus = 3;
                await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
            //履约计划全部是完成，合约为履约中改为完成
            if (list.All(u => u.Plan == 3) && query.ContractStatus == 3)
            {
                query.ContractStatus = 4;
                query.EndTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
                await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
            //履约计划全部是中止，合约为履约中改为中止
            if (list.All(u => u.Plan == 4))
            {
                query.ContractStatus = 5;
                query.EndTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
                await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
            await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
    }
}
