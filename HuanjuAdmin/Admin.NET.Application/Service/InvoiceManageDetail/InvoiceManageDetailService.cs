﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;

namespace Admin.NET.Application;
/// <summary>
/// 发票明细管理服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class InvoiceManageDetailService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<InvoiceManageDetail> _rep;
    public InvoiceManageDetailService(SqlSugarRepository<InvoiceManageDetail> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询发票明细管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<InvoiceManageDetailOutput>> Page(InvoiceManageDetailInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.InvoiceManageId > 0, u => u.InvoiceManageId == input.InvoiceManageId)
                    .WhereIF(input.GoodsId > 0, u => u.GoodsId == input.GoodsId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.GoodsName), u => u.GoodsName.Contains(input.GoodsName.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Specs), u => u.Specs.Contains(input.Specs.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
                    .WhereIF(input.SalesNum > 0, u => u.SalesNum == input.SalesNum)
                    .WhereIF(input.TaxTag > 0, u => u.TaxTag == input.TaxTag)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.GoodsCode), u => u.GoodsCode.Contains(input.GoodsCode.Trim()))

                    .Select(u => new InvoiceManageDetailOutput
                    {
                        Id = u.Id,
                        InvoiceManageId = u.InvoiceManageId,
                        GoodsId = u.GoodsId,
                        GoodsName = u.GoodsName,
                        Specs = u.Specs,
                        Unit = u.Unit,
                        SalesNum = u.SalesNum,
                        Price = u.Price,
                        Amount = u.Amount,
                        TaxRate = u.TaxRate,
                        Tax = u.Tax,
                        TaxTag = u.TaxTag,
                        GoodsCode = u.GoodsCode,
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    public async Task<int> AddOrUpdate(List<InvoiceManageDetail> listMx)
    {
        return await _rep.AsSugarClient().Storageable(listMx).ExecuteCommandAsync();
    }

    /// <summary>
    /// 增加发票明细管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddInvoiceManageDetailInput input)
    {
        var entity = input.Adapt<InvoiceManageDetail>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除发票明细管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteInvoiceManageDetailInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.DeleteAsync(entity); //删除
    }

    /// <summary>
    /// 更新发票明细管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateInvoiceManageDetailInput input)
    {
        var entity = input.Adapt<InvoiceManageDetail>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    ///// <summary>
    ///// 获取发票明细管理
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<InvoiceManageDetail> Get([FromQuery] QueryByIdInvoiceManageDetailInput input)
    //{
    //    return null;
    //}

    /// <summary>
    /// 获取发票明细管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<InvoiceManageDetailOutput>> List([FromQuery] InvoiceManageDetailInput input)
    {
        return await _rep.AsQueryable().Select<InvoiceManageDetailOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取发票ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "InvoiceManageDropdown"), HttpGet]
    public async Task<dynamic> InvoiceManageDropdown()
    {
        return await _rep.Context.Queryable<InvoiceManage>()
                .Select(u => new
                {
                    Label = u.Id,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取商品ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "EshopGoodsDropdown"), HttpGet]
    public async Task<dynamic> EshopGoodsDropdown()
    {
        return await _rep.Context.Queryable<EshopGoods>()
                .Select(u => new
                {
                    Label = u.Id,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

