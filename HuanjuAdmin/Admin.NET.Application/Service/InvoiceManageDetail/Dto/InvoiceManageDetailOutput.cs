﻿namespace Admin.NET.Application;

    /// <summary>
    /// 发票明细管理输出参数
    /// </summary>
    public class InvoiceManageDetailOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 发票ID
       /// </summary>
       public long? InvoiceManageId { get; set; } 
       
    
       /// <summary>
       /// 商品ID
       /// </summary>
       public long? GoodsId { get; set; } 
    
       /// <summary>
       /// 商品名称
       /// </summary>
       public string? GoodsName { get; set; }
    
       /// <summary>
       /// 规格
       /// </summary>
       public string? Specs { get; set; }
    
       /// <summary>
       /// 单位
       /// </summary>
       public string? Unit { get; set; }
    
       /// <summary>
       /// 数量
       /// </summary>
       public int? SalesNum { get; set; }
    
       /// <summary>
       /// 单价
       /// </summary>
       public decimal? Price { get; set; }
    
       /// <summary>
       /// 金额
       /// </summary>
       public decimal? Amount { get; set; }
    
       /// <summary>
       /// 税率
       /// </summary>
       public decimal? TaxRate { get; set; }
    
       /// <summary>
       /// 税额
       /// </summary>
       public decimal? Tax { get; set; }
    
       /// <summary>
       /// 含税标志
       /// </summary>
       public int? TaxTag { get; set; }
    
       /// <summary>
       /// 商品编码
       /// </summary>
       public string? GoodsCode { get; set; }
    
    }
 

