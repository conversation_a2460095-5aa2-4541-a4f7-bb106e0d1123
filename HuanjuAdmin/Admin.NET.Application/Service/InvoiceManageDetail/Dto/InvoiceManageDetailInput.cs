﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 发票明细管理基础输入参数
    /// </summary>
    public class InvoiceManageDetailBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 发票ID
        /// </summary>
        public virtual long? InvoiceManageId { get; set; }
        
        /// <summary>
        /// 商品ID
        /// </summary>
        public virtual long? GoodsId { get; set; }
        
        /// <summary>
        /// 商品名称
        /// </summary>
        public virtual string? GoodsName { get; set; }
        
        /// <summary>
        /// 规格
        /// </summary>
        public virtual string? Specs { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public virtual string? Unit { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public virtual int? SalesNum { get; set; }
        
        /// <summary>
        /// 单价
        /// </summary>
        public virtual decimal? Price { get; set; }
        
        /// <summary>
        /// 金额
        /// </summary>
        public virtual decimal? Amount { get; set; }
        
        /// <summary>
        /// 税率
        /// </summary>
        public virtual decimal? TaxRate { get; set; }
        
        /// <summary>
        /// 税额
        /// </summary>
        public virtual decimal? Tax { get; set; }
        
        /// <summary>
        /// 含税标志
        /// </summary>
        public virtual int? TaxTag { get; set; }
        
        /// <summary>
        /// 商品编码
        /// </summary>
        public virtual string? GoodsCode { get; set; }
        
    }

    /// <summary>
    /// 发票明细管理分页查询输入参数
    /// </summary>
    public class InvoiceManageDetailInput : BasePageInput
    {
        /// <summary>
        /// 发票ID
        /// </summary>
        public long? InvoiceManageId { get; set; }
        
        /// <summary>
        /// 商品ID
        /// </summary>
        public long? GoodsId { get; set; }
        
        /// <summary>
        /// 商品名称
        /// </summary>
        public string? GoodsName { get; set; }
        
        /// <summary>
        /// 规格
        /// </summary>
        public string? Specs { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int? SalesNum { get; set; }
        
        /// <summary>
        /// 单价
        /// </summary>
        public decimal? Price { get; set; }
        
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? Amount { get; set; }
        
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? Tax { get; set; }
        
        /// <summary>
        /// 含税标志
        /// </summary>
        public int? TaxTag { get; set; }
        
        /// <summary>
        /// 商品编码
        /// </summary>
        public string? GoodsCode { get; set; }
        
    }

    /// <summary>
    /// 发票明细管理增加输入参数
    /// </summary>
    public class AddInvoiceManageDetailInput : InvoiceManageDetailBaseInput
    {
    }

    /// <summary>
    /// 发票明细管理删除输入参数
    /// </summary>
    public class DeleteInvoiceManageDetailInput : BaseIdInput
    {
    }

    /// <summary>
    /// 发票明细管理更新输入参数
    /// </summary>
    public class UpdateInvoiceManageDetailInput : InvoiceManageDetailBaseInput
    {
    }

    /// <summary>
    /// 发票明细管理主键查询输入参数
    /// </summary>
    public class QueryByIdInvoiceManageDetailInput : DeleteInvoiceManageDetailInput
    {

    }
