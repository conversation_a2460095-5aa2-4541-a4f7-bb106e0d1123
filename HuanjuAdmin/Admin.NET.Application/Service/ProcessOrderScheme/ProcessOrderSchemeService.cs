﻿using System.Linq;
using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using SqlSugar;

namespace Admin.NET.Application;
/// <summary>
/// 加工单配置管理服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class ProcessOrderSchemeService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ProcessOrderScheme> _rep;
    private readonly UserManager _userManager;
    public ProcessOrderSchemeService(SqlSugarRepository<ProcessOrderScheme> rep,
     UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询加工单配置管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<ProcessOrderScheme>> Page(ProcessOrderSchemeInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SchemeNo), u => u.SchemeNo.Contains(input.SchemeNo.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SchemeName), u => u.SchemeName.Contains(input.SchemeName.Trim()))
                    .WhereIF(input.Status > 0, u => u.Status == input.Status)
                    .Includes(x => x.MaterialWarehouse)
                    .Includes(x => x.ProduceWarehouse)
                    .Includes(x=>x.CreateUser)
                    ;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    [HttpPost]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<ProcessOrderScheme> GetOne(ProcessOrderSchemeBaseInput input)
    {
        return await _rep.AsQueryable().Where(u => u.Id == input.Id)
        .Includes(x => x.MaterialList.Select(m => new ProcessOrderSchemeMaterial
        {
            Id = m.Id,
            ProcessOrderSchemeId = m.ProcessOrderSchemeId,
            WarehouseGoodsId = m.WarehouseGoodsId,
            Quantity = m.Quantity,
        }).ToList(), m => m.Warehousegoods, g => g.WarehouseGoodsUnit)
        .Includes(x => x.ProduceList.Select(p => new ProcessOrderSchemeProduce
        {
            Id = p.Id,
            ProcessOrderSchemeId = p.ProcessOrderSchemeId,
            WarehouseGoodsId = p.WarehouseGoodsId,
            Quantity = p.Quantity,
            UnitPrice = p.UnitPrice,
        }).ToList(), p => p.Warehousegoods, g => g.WarehouseGoodsUnit)
        .FirstAsync();
    }



    /// <summary>
    /// 增加加工单配置管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddProcessOrderSchemeInput input)
    {
        var entity = input.Adapt<ProcessOrderScheme>();
        await _rep.Context.InsertNav(entity, new InsertNavRootOptions()
        {
            IgnoreColumns = new string[] { nameof(entity.UpdateUserId), nameof(entity.UpdateTime) }//InsertColumns也可以用只插入哪几列
        })
        .Include(x => x.MaterialList)
        .Include(x => x.ProduceList)
        .ExecuteCommandAsync();

        // await _rep.Context.Ado.BeginTranAsync();
        // await _rep.InsertAsync(entity);
        // foreach (var item in entity.MaterialList)
        // {
        //     item.ProcessOrderSchemeId = entity.Id;
        //     await _rep.Context.Insertable(item).ExecuteCommandAsync();
        // }
        // foreach (var item in entity.OutputList)
        // {
        //     item.ProcessOrderSchemeId = entity.Id;
        //     await _rep.Context.Insertable(item).ExecuteCommandAsync();
        // }
        // await _rep.Context.Ado.CommitTranAsync();
        //await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除加工单配置管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteProcessOrderSchemeInput input)
    {
        var entity = input.Adapt<ProcessOrderScheme>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新加工单配置管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateProcessOrderSchemeInput input)
    {
        var entity = input.Adapt<ProcessOrderScheme>();


        await _rep.Context.UpdateNav(entity, new UpdateNavRootOptions()
        {
            IgnoreColumns = new string[] { nameof(entity.CreateUserId), nameof(entity.CreateTime) }
        })
        .Include(x => x.MaterialList)
        .Include(x => x.ProduceList)
        .ExecuteCommandAsync();

        // await _rep.Context.Ado.BeginTranAsync();
        // await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        // foreach (var item in entity.MaterialList)
        // {
        //     if(item.Id == 0)
        //     {
        //         item.ProcessOrderSchemeId = entity.Id;
        //         await _rep.Context.Insertable(item).ExecuteCommandAsync();
        //     }
        //     else
        //     {
        //         await _rep.AsUpdateable(item).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        //     }
        // }
        // foreach (var item in entity.OutputList)
        // {
        //     item.ProcessOrderSchemeId = entity.Id;
        //     await _rep.Context.Insertable(item).ExecuteCommandAsync();
        // }
        // await _rep.Context.Ado.CommitTranAsync();

    }

    // /// <summary>
    // /// 获取加工单配置管理
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [HttpGet]
    // [ApiDescriptionSettings(Name = "Detail")]
    // public async Task<ProcessOrderScheme> Get([FromQuery] QueryByIdProcessOrderSchemeInput input)
    // {
    // }

    /// <summary>
    /// 获取加工单配置管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<ProcessOrderSchemeOutput>> List([FromQuery] ProcessOrderSchemeInput input)
    {
        return await _rep.AsQueryable().Select<ProcessOrderSchemeOutput>().ToListAsync();
    }



    /// <summary>
    /// 获取仓库列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseList")]
    public async Task<List<WarehouseOutput>> GetWarehouseList(WarehouseInput input)
    {
        return await _rep.Context.Queryable<Warehouse>()
                .Where(x => x.Status == 1)
                .Select<WarehouseOutput>(
                    x => new WarehouseOutput
                    {
                        Id = x.Id,
                        Name = x.Name
                    }
                ).ToListAsync();
    }

    /// <summary>
    /// 获取仓库商品列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseGoodsList")]
    public async Task<List<ProcessOrderSchemeMaterial>> GetWarehouseGoodsList(WarehousegoodsInput input)
    {
        return (await _rep.Context.Queryable<Warehousegoods>()
        .Includes(x => x.WarehouseGoodsUnit)
        .ToListAsync()).Select(x => new ProcessOrderSchemeMaterial
        {
            WarehouseGoodsId = x.Id,
            Warehousegoods = x
        }).ToList();
    }

    /// <summary>
    /// 获取仓库库存列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseStoreList")]
    public async Task<List<ProcessOrderSchemeMaterial>> GetWarehouseStoreList(WarehouseStore input)
    {
        return (await _rep.Context.Queryable<WarehouseStore>()
                .WhereIF(input.WarehouseId > 0, x => x.WarehouseId == input.WarehouseId)
                .Includes(x => x.Warehousegoods, m => m.WarehouseGoodsUnit)
                .ToListAsync())
                .Select(x => new ProcessOrderSchemeMaterial
                {
                    WarehouseGoodsId = x.Warehousegoods.Id,
                    Warehousegoods = x.Warehousegoods
                })
                .GroupBy(x=>x.WarehouseGoodsId)
                .Select(g=>new ProcessOrderSchemeMaterial
                {
                    WarehouseGoodsId = g.Key,
                    Warehousegoods = g.First().Warehousegoods
                })
                .ToList();

    }

}

