﻿using Admin.NET.Application.Entity;

namespace Admin.NET.Application;

    /// <summary>
    /// 加工单配置管理输出参数
    /// </summary>
    public class ProcessOrderSchemeOutput
    {
      /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 方案编号
        /// </summary>
        public string SchemeNo { get; set; }
        
        /// <summary>
        /// 方案名称
        /// </summary>
        public string SchemeName { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 原料仓库
        /// </summary>
        public long MaterialWarehouseId { get; set; }

        /// <summary>
        /// 成品仓库
        /// </summary>
        public long ProduceWarehouseId { get; set; }

        /// <summary>
        /// 加工单配置
        /// </summary>
        public List<ProcessOrderSchemeProduce>? ProduceList { get; set; }


        /// <summary>
        /// 加工单配置
        /// </summary>
        public List<ProcessOrderSchemeMaterial>? MaterialList { get; set; }
    
    }
 

