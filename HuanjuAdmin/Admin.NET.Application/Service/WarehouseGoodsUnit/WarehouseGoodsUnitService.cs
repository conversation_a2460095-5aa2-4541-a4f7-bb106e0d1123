﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Identity;

namespace Admin.NET.Application;
/// <summary>
/// 商品单位服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseGoodsUnitService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WarehouseGoodsUnit> _rep;
    UserManager _userManager;
    public WarehouseGoodsUnitService(SqlSugarRepository<WarehouseGoodsUnit> rep, UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询商品单位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseGoodsUnitOutput>> Page(WarehouseGoodsUnitInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(x => x.IsDelete == false)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name == input.Name)
                    .WhereIF(!input.Status.IsNullOrEmpty(), u => u.Status == input.Status)
                     .Where(u => u.TenantId == _userManager.TenantId)
                    .Select<WarehouseGoodsUnitOutput>();

        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加商品单位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseGoodsUnitInput input)
    {
        var entity = input.Adapt<WarehouseGoodsUnit>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品单位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseGoodsUnitInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品单位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseGoodsUnitInput input)
    {
        var entity = input.Adapt<WarehouseGoodsUnit>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取商品单位
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehouseGoodsUnit> Get([FromQuery] QueryByIdWarehouseGoodsUnitInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品单位列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "List")]
    //public async Task<List<WarehouseGoodsUnitOutput>> List([FromQuery] WarehouseGoodsUnitInput input)
    //{
    //    return await _rep.AsQueryable().Where(u => u.TenantId == _userManager.TenantId).Where(x => x.IsDelete == false).Select<WarehouseGoodsUnitOutput>().ToListAsync();
    //}

    /// <summary>
    /// 获取商品单位编号列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "List"), HttpGet]
    public async Task<dynamic> List()
    {
        return await _rep.Context.Queryable<WarehouseGoodsUnit>()
                .Where(u => u.TenantId == _userManager.TenantId)
                .Where(x => x.IsDelete == false && x.Status)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }



}

