﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;

namespace Admin.NET.Application;
/// <summary>
/// 固资领用服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class AssetBorrowService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<AssetBorrow> _rep;
    private readonly SqlSugarRepository<AssetInventory> _repInventory;
    public AssetBorrowService(
        UserManager userManager,
        SqlSugarRepository<AssetBorrow> rep,
        SqlSugarRepository<AssetInventory> repInventory)
    {
        _userManager = userManager;
        _rep = rep;
        _repInventory = repInventory;
    }

    /// <summary>
    /// 分页查询固资领用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<AssetBorrowOutput>> Page(AssetBorrowInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .WhereIF(input.UserId > 0, u => u.UserId == input.UserId)
                    .WhereIF(input.AssetId > 0, u => u.AssetId == input.AssetId)

                    .Select(u => new AssetBorrowOutput
                    {
                        Id = u.Id,
                        UserId = u.UserId,
                        SysUserRealName = u.SysUser.RealName,
                        AssetId = u.AssetId,
                        AssetInventoryName = u.AssetInventory.Name,
                        BorrowCount = u.BorrowCount,
                        ReturnCount = u.ReturnCount,
                        IsNdReturn = u.IsNdReturn,
                        Remark = u.Remark,
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加固资领用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddAssetBorrowInput input)
    {
        var entity = input.Adapt<AssetBorrow>();
        await _rep.InsertAsync(entity);

        //同步更新库存，库存数量-=（借出数量-归还数量）
        var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);

        //如果是不需要归还的，要同步扣减总库存
        if (!entity.IsNdReturn)
        {
            assetInventory.TotalCount -= entity.BorrowCount - entity.ReturnCount;
        }
        assetInventory.InventoryCount -= entity.BorrowCount - entity.ReturnCount;
        await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除固资领用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteAssetBorrowInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除

        //同步更新库存，库存数量-=（借出数量-归还数量）
        var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);

        //如果是不需要归还的，要同步增加总库存
        if (!entity.IsNdReturn)
        {
            assetInventory.TotalCount += entity.BorrowCount - entity.ReturnCount;
        }
        assetInventory.InventoryCount += entity.BorrowCount - entity.ReturnCount;
        await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新固资领用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateAssetBorrowInput input)
    {
        var oringEntity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        var entity = input.Adapt<AssetBorrow>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        if (oringEntity.BorrowCount != entity.BorrowCount || oringEntity.ReturnCount != entity.ReturnCount || oringEntity.IsNdReturn != entity.IsNdReturn)
        {
            var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);
            if (oringEntity.IsNdReturn != entity.IsNdReturn)
            {
                if (oringEntity.IsNdReturn)
                {
                    assetInventory.TotalCount -= entity.BorrowCount - entity.ReturnCount;
                }
                else
                {
                    assetInventory.TotalCount += oringEntity.BorrowCount - oringEntity.ReturnCount;
                }
            }

            //同步更新库存，库存数量-=借出数量-归还数量-（旧借出数量-旧归还数量）
            assetInventory.InventoryCount -= entity.BorrowCount - entity.ReturnCount - (oringEntity.BorrowCount - oringEntity.ReturnCount);
            await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取固资领用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<AssetBorrow> Get([FromQuery] QueryByIdAssetBorrowInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取固资领用列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<AssetBorrowOutput>> List([FromQuery] AssetBorrowInput input)
    {
        return await _rep.AsQueryable().Select<AssetBorrowOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取领用人列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysUserDropdown"), HttpGet]
    public async Task<dynamic> SysUserDropdown()
    {
        return await _rep.Context.Queryable<SysUser>()
                .Where(u => u.TenantId == _userManager.TenantId)
                .Select(u => new
                {
                    Label = u.RealName,
                    Value = u.Id,
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取固资Id列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "AssetInventoryDropdown"), HttpGet]
    public async Task<dynamic> AssetInventoryDropdown()
    {
        return await _rep.Context.Queryable<AssetInventory>()
                .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id,
                    InventoryCount = u.InventoryCount,
                    IsNdReturn = u.IsNdReturn,
                }
                ).ToListAsync();
    }




}

