﻿namespace Admin.NET.Application;

    /// <summary>
    /// 固资领用输出参数
    /// </summary>
    public class AssetBorrowDto
    {
        /// <summary>
        /// 领用人
        /// </summary>
        public string SysUserRealName { get; set; }
        
        /// <summary>
        /// 固资Id
        /// </summary>
        public string AssetInventoryName { get; set; }
        
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 领用人
        /// </summary>
        public long UserId { get; set; }
        
        /// <summary>
        /// 固资Id
        /// </summary>
        public long AssetId { get; set; }
        
        /// <summary>
        /// 领用数量
        /// </summary>
        public int BorrowCount { get; set; }
        
        /// <summary>
        /// 归还数量
        /// </summary>
        public int ReturnCount { get; set; }
        
        /// <summary>
        /// 需要归还
        /// </summary>
        public bool IsNdReturn { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        
    }
