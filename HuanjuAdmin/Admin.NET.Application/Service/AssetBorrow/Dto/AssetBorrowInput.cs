﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 固资领用基础输入参数
    /// </summary>
    public class AssetBorrowBaseInput
    {
        /// <summary>
        /// 领用人
        /// </summary>
        public virtual long UserId { get; set; }
        
        /// <summary>
        /// 固资Id
        /// </summary>
        public virtual long AssetId { get; set; }
        
        /// <summary>
        /// 领用数量
        /// </summary>
        public virtual int BorrowCount { get; set; }
        
        /// <summary>
        /// 归还数量
        /// </summary>
        public virtual int ReturnCount { get; set; }
        
        /// <summary>
        /// 需要归还
        /// </summary>
        public virtual bool IsNdReturn { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
    }

    /// <summary>
    /// 固资领用分页查询输入参数
    /// </summary>
    public class AssetBorrowInput : BasePageInput
    {
        /// <summary>
        /// 领用人
        /// </summary>
        public long UserId { get; set; }
        
        /// <summary>
        /// 固资Id
        /// </summary>
        public long AssetId { get; set; }
        
        /// <summary>
        /// 需要归还
        /// </summary>
        public bool IsNdReturn { get; set; }
        
    }

    /// <summary>
    /// 固资领用增加输入参数
    /// </summary>
    public class AddAssetBorrowInput : AssetBorrowBaseInput
    {
    }

    /// <summary>
    /// 固资领用删除输入参数
    /// </summary>
    public class DeleteAssetBorrowInput : BaseIdInput
    {
    }

    /// <summary>
    /// 固资领用更新输入参数
    /// </summary>
    public class UpdateAssetBorrowInput : AssetBorrowBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 固资领用主键查询输入参数
    /// </summary>
    public class QueryByIdAssetBorrowInput : DeleteAssetBorrowInput
    {

    }
