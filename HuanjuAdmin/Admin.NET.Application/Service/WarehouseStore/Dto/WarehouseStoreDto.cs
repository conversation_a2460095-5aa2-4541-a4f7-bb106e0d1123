﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 库存查询输出参数
    /// </summary>
    public class WarehouseStoreDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 序号
        /// </summary>
        public int? Number { get; set; }
        
        /// <summary>
        /// 仓库
        /// </summary>
        public string? Warehouse { get; set; }
        
        /// <summary>
        /// 库存数量
        /// </summary>
        public int? Quantity { get; set; }
        
        /// <summary>
        /// 商品名称
        /// </summary>
        public string? TradeName { get; set; }
        
        /// <summary>
        /// 品牌
        /// </summary>
        public string? Brand { get; set; }
        
        /// <summary>
        /// 规格
        /// </summary>
        public string? Specifications { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
        
        /// <summary>
        /// 是否保质期
        /// </summary>
        public bool? IsWarranty { get; set; }
        
        /// <summary>
        /// 供应商
        /// </summary>
        public string? Supplier { get; set; }
        
        /// <summary>
        /// 商品编码
        /// </summary>
        public string? ProductCode { get; set; }
        
        /// <summary>
        /// 商品条码
        /// </summary>
        public string? BarCode { get; set; }
        
        /// <summary>
        /// 是否唯一码
        /// </summary>
        public bool? IsUniqueCode { get; set; }
        
        /// <summary>
        /// 保质期
        /// </summary>
        public int? Warranty { get; set; }
        
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProduceTime { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
        
    }
