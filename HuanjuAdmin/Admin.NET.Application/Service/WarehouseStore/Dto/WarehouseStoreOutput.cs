﻿using SqlSugar;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 库存查询输出参数
/// </summary>
public class WarehouseStoreOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int? Number { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    public long? WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    public string? Warehouse { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public int? Quantity { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long TradeID { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string? TradeName { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 是否保质期
    /// </summary>
    public bool? IsWarranty { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string? Supplier { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? ProductCode { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? BarCode { get; set; }

    /// <summary>
    /// 是否唯一码
    /// </summary>
    public bool IsUniqueCode { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public int? Warranty { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProduceTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }


    /// <summary>
    /// 待出库数
    /// </summary>
    public int ShippedoutNum { get; set; }

    /// <summary>
    /// 在途数
    /// </summary>
    public int IntransitNum { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public int SafetyStockTallNum { get; set; }


    /// <summary>
    /// 最低安全库存数
    /// </summary>
    public int SafetyStockLowNum { get; set; }
    

    /// <summary>
    /// 七日销量
    /// </summary>
    public int SevenSalesNum { get; set; }

    /// <summary>
    /// 临期提醒
    /// </summary>
    public string AdventRemind { get; set; }

    /// <summary>
    /// 当前采购单价
    /// </summary>
    public decimal PurchaseUnitPrice { get; set; }

    /// <summary>
    /// 当前成本
    /// </summary>
    public decimal CurrentCost { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool StockOrNot { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public int? ExpiredWarning { get; set; }

    /// <summary>
    /// 库存
    /// </summary>
    public int? StockWarning { get; set; }

    /// <summary>
    /// 缺货数量
    /// </summary>
    public int StockNum { get; set; }

    /// <summary>
    /// 良品数量
    /// </summary>
    public int GoodProduct { get; set; }

    /// <summary>
    /// 次品数量
    /// </summary>
    public int Reject { get; set; }

    /// <summary>
    /// 可销售数
    /// </summary>
    public int Marketable { get; set; }

    /// <summary>
    /// 销售占用数
    /// </summary>
    public int SalesOccupancy { get; set; }

    /// <summary>
    /// 可配数
    /// </summary>
    public int Compatible { get; set; }

    /// <summary>
    /// 是否批次
    /// </summary>
    public bool isbatch { get; set; }

}


