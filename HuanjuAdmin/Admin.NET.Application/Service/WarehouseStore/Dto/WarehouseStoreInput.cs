﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 库存查询基础输入参数
/// </summary>
public class WarehouseStoreBaseInput
{

    /// <summary>
    /// 商品ID
    /// </summary>
    public long TradeID { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    public virtual long? WarehouseId { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    public virtual string? Warehouse { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public virtual int? Quantity { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public virtual string? TradeName { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public virtual string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 是否保质期
    /// </summary>
    public virtual bool? IsWarranty { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public virtual string? Supplier { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public virtual string? ProductCode { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public virtual string? BarCode { get; set; }

    /// <summary>
    /// 是否唯一码
    /// </summary>
    public virtual bool? IsUniqueCode { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public virtual int? Warranty { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProduceTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Notes { get; set; }

    /// <summary>
    /// 良品数量
    /// </summary>
    public virtual int? GoodProduct { get; set; }

    /// <summary>
    /// 次品数量
    /// </summary>
    public virtual int? Reject { get; set; }

    /// <summary>
    /// 待出库数
    /// </summary>
    public int? ShippedoutNum { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public virtual int? SafetyStockTallNum { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public virtual int? SafetyStockLowNum { get; set; }


    public string batchnumber { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal PurchaseUnitPrice { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public decimal CurrentCost { get; set; }
    public int type { get; set; }
    public int? connvertNum { get; set; }
    public bool? isbatch { get; set; }

}

/// <summary>
/// 库存查询分页查询输入参数
/// </summary>
public class WarehouseStoreInput : BasePageInput
{
    /// <summary>
    /// 仓库
    /// </summary>
    public string? Warehouse { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string? TradeName { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string? Supplier { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool? StockOrNot { get; set; }


    /// <summary>
    /// 保质期状态过滤条件
    /// 可选值：
    /// -1：未设置
    /// 0：正常
    /// 1：临期
    /// 2：过期
    /// </summary>
    public int[] ExpiryStatus { get; set; }

    /// <summary>
    /// 库存预警状态过滤条件
    /// 可选值：
    /// -1：未设置
    /// 0：正常
    /// 1：库存不足
    /// 2：库存过剩
    /// </summary>
    public int[] StockStatus { get; set; }

}

/// <summary>
/// 库存查询增加输入参数
/// </summary>
public class Warehousing
{
    public long Id { get; set; }
    public long goodsId { get; set; }
    /// <summary>
    /// 入库单号
    /// </summary>
    public virtual long inrecordId { get; set; }

    /// <summary>
    /// 单据数量
    /// </summary>
    public int? puchQty { get; set; }
    /// <summary>
    /// 仓库goodsId
    /// </summary>
    public virtual string warehouse { get; set; }

    /// <summary>
    /// 入库数量
    /// </summary>
    public virtual int quantity { get; set; }

    /// <summary>
    /// 商品名称warehousegoodsName
    /// </summary>
    public virtual string tradeName { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public virtual string brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string unit { get; set; }

    /// <summary>
    /// 是否保质期
    /// </summary>
    public virtual bool isWarranty { get; set; }

    /// <summary>
    /// 供应商supplierId
    /// </summary>
    public virtual string supplier { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public virtual string productCode { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public virtual string barCode { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public virtual bool isUniqueCode { get; set; }

    /// <summary>
    /// 保质期expires
    /// </summary>
    public virtual string warranty { get; set; }

    /// <summary>
    /// 生产日期productDate
    /// </summary>
    public virtual DateTime? produceTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string notes { get; set; }

    /// <summary>
    /// 良品数量
    /// </summary>
    public virtual int? goodProduct { get; set; }


    /// <summary>
    /// 次品数量
    /// </summary>
    public virtual int? reject { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public virtual int? safetyStockTallNum { get; set; }

    /// <summary>
    /// 最高安全库存数
    /// </summary>
    public virtual int? safetyStockLowNum { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public virtual decimal Unitprice { get; set; }

    public string batchnumber { get; set; }


    /// <summary>
    /// 单据数量
    /// </summary>
    public int documentNum { get; set; }

    public bool isproduct { get; set; }
    public List<AddwarehousebatchInput> batchs { get; set; }

}

/// <summary>
/// 库存查询增加输入参数
/// </summary>
public class AddWarehouseStoreInput
{
}
/// <summary>
/// 库存查询删除输入参数
/// </summary>
public class DeleteWarehouseStoreInput : BaseIdInput
{
}

/// <summary>
/// 库存查询更新输入参数
/// </summary>
public class UpdateWarehouseStoreInput : WarehouseStoreBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }
    public long? BatchId { get; set; }
}

/// <summary>
/// 库存查询主键查询输入参数
/// </summary>
public class QueryByIdWarehouseStoreInput : DeleteWarehouseStoreInput
{

}
