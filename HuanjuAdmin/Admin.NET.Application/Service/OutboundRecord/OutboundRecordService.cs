﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.OutboundRecord.Dto;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Admin.NET.Application;
/// <summary>
/// 出库记录服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class OutboundRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<OutboundRecord> _rep;
    private readonly SqlSugarRepository<Warehouseout> _repout;
    private readonly SqlSugarRepository<Warehousegoods> _repGoods;
    private readonly SqlSugarRepository<OutInBoundRecord> _repOI;
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<OutboundRecord> _repO;
    private readonly SqlSugarRepository<InboundRecord> _repI;
    private readonly SqlSugarRepository<WarehouseoutMX> _repmx;
    private readonly SqlSugarRepository<WarehouseStore> _repStore;
    private readonly SqlSugarRepository<warehousebatch> _repbatch;
    private readonly SqlSugarRepository<WarehouseBatchUpdateQueue> _repWarehouseBatchUpdateQueue;
    private readonly SqlSugarRepository<WarehouseInrecord> _repIn;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _repInMx;
    public OutboundRecordService(SqlSugarRepository<OutboundRecord> rep, SqlSugarRepository<OutInBoundRecord> repOI,SqlSugarRepository<Warehouseout> repout,SqlSugarRepository<Warehousegoods> repGoods, UserManager userManager, SqlSugarRepository<OutboundRecord> repO, SqlSugarRepository<InboundRecord> repI, SqlSugarRepository<WarehouseoutMX> repmx, SqlSugarRepository<WarehouseStore> repStore, SqlSugarRepository<warehousebatch> repbatch, SqlSugarRepository<WarehouseBatchUpdateQueue> repWarehouseBatchUpdateQueue, SqlSugarRepository<WarehouseInrecord> repIn, SqlSugarRepository<WarehouseInrecordMX> repInMx)
    {
        _rep = rep;
        _repOI = repOI;
        _repout = repout;
        _repGoods = repGoods;
        _userManager = userManager;
        _repO = repO;
        _repI = repI;
        _repmx = repmx;
        _repStore = repStore;
        _repbatch = repbatch;
        _repWarehouseBatchUpdateQueue = repWarehouseBatchUpdateQueue;
        _repIn = repIn;
        _repInMx = repInMx;
    }

    /// <summary>
    /// 分页查询出入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<OutInBoundOutput>> Page(OutboundRecordInput input)
    {
        var query = _repOI.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Type), u => u.Type == input.Type)
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrintState), u => input.PrintState == "0" ? u.PrintCount == 0 : u.PrintCount > 0)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber == input.OrderNumber)
            .WhereIF(input.CustomId != null, u => u.CustomId == input.CustomId)
            .WhereIF(input.SupplierId != null, u => u.SupplierId == input.SupplierId)
            .WhereIF(input.WarehouseId != null, u => u.WarehouseId == input.WarehouseId)
            .WhereIF(input.GoodsId != null, u => u.GoodsId == input.GoodsId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNumber), u => u.BatchNumber == input.BatchNumber)
            .WhereIF(input.StartTime != null, u => u.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime != null, u => u.CreateTime < input.EndTime)
            .WhereIF(input.CreateUserId != null, u => u.CreateUserId < input.CreateUserId)
            .Select<OutInBoundOutput>();
        query = query.OrderBuilder(input);
        var list = await query.ToPagedListAsync(input.Page, input.PageSize);
        return list;
    }

    /// <summary>
    /// 分页查询出入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<OutInBoundOutput>> List(OutInBoundInput input)
    {
        var query = _repOI.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Type), u => u.Type == input.Type)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber == input.OrderNumber)
            .OrderByDescending(u => u.CreateTime)
            .Select<OutInBoundOutput>();
        var list = await query.ToListAsync();
        return list;
    }

    /// <summary>
    /// 增加出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<OutboundRecord> Get([FromQuery] QueryByIdOutboundRecordInput input)
    {
        return await _rep.GetByIdAsync(input.Id);
    }


    /// <summary>
    /// 更新打印次数
    /// </summary>
    /// <param name="records"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdatePrintCount")]
    public async Task UpdatePrintCountAsync([FromBody] List<PrintRecord> records)
    {
        List<long> listO = new List<long>();
        List<long> listI = new List<long>();
        if (records != null)
        {
            foreach (var item in records)
            {
                if (item.Type == "0")
                {
                    listO.Add(item.Id);
                }
                else if (item.Type == "1")
                {
                    listI.Add(item.Id);
                }
            }
        }

        if (listO.Count > 0)
        {
            await _repO.AsUpdateable()
                .SetColumns(it => new OutboundRecord
                {
                    PrintCount = it.PrintCount + 1,
                    UpdateUserId = _userManager.UserId,
                    UpdateTime = SqlFunc.GetDate()
                })
                .Where(x => listO.Contains(x.Id)).ExecuteCommandAsync();
        }

        if (listI.Count > 0)
        {
            await _repI.AsUpdateable().
                SetColumns(it => new InboundRecord
                {
                    PrintCount = it.PrintCount + 1,
                    UpdateUserId = _userManager.UserId,
                    UpdateTime = SqlFunc.GetDate()
                })
                .Where(x => listI.Contains(x.Id)).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 出库记录红冲
    /// </summary>
    /// <param name="outboundRecordId">需要红冲的出库记录ID</param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "RedInkOutboundRecord")]
    public async Task RedInkOutboundRecord([FromQuery] long outboundRecordId)
    {
        // 获取原出库记录
        var outboundRecord = await _rep.GetFirstAsync(x => x.Id == outboundRecordId)
            ?? throw Oops.Oh("未找到出库记录");

        // 验证红冲限制
        // 1. 检查是否已经被红冲过
        if (outboundRecord.RedInkStatus == 1)
            throw Oops.Oh("该出库记录已经被红冲过，不允许再次红冲");

        // 2. 检查本身是否是红冲记录
        if (outboundRecord.RedInkStatus == 2)
            throw Oops.Oh("红冲记录不允许再次红冲");

        // 3. 兼容性检查：通过流水号和数量判断（用于没有RedInkStatus字段的旧数据）
        if (outboundRecord.OrderNum?.Contains("-红冲") == true)
            throw Oops.Oh("红冲记录不允许再次红冲");

        if (outboundRecord.OutBoundCount < 0)
            throw Oops.Oh("红冲记录不允许再次红冲");

        // 获取出库明细
        var outMx = await _repmx.GetFirstAsync(x => x.Id == outboundRecord.WarehouseOutMxId)
            ?? throw Oops.Oh("未找到对应的出库明细");

        // 获取出库单
        var outOrder = await _repout.GetFirstAsync(x => x.Id == outMx.OutId)
            ?? throw Oops.Oh("未找到对应的出库单");

        // 验证是否可以红冲
        if (outOrder.OutboundStatus != 2 && outOrder.OutboundStatus != 3)
            throw Oops.Oh("只有部分出库或完全出库的单据才能进行红冲操作");

        // 生成红冲记录编号
        string redInkOrderNum = outboundRecord.OrderNum + "-红冲";

        // 获取库存信息 - 使用WarehouseStore类型
        var querysale = await _repStore.GetFirstAsync(x => x.TradeID == outMx.goodsId && x.WarehouseId == outOrder.WarehouseId)
            ?? throw Oops.Oh($"未找到商品ID为{outMx.goodsId}的库存信息");

        // 获取商品信息
        var goods = await _repGoods.GetByIdAsync(outMx.goodsId);

        // 红冲数量
        int redInkCount = outboundRecord.OutBoundCount.ToInt(0);

        // 出库明细实际出库数量调整
        outMx.TrueOutCount = outMx.TrueOutCount.ToInt(0) - redInkCount;
        await _repmx.UpdateAsync(outMx);

        // 处理库存 - 红冲时增加库存
        if (outboundRecord.GoodProduct == true)
        {
            querysale.GoodProduct = querysale.GoodProduct.ToInt(0) + redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) + redInkCount;
            
            // 重新计算库存相关数据（待出库数、可配数、缺货状态）
            await RecalculateInventoryData(querysale, outOrder.WarehouseId, outMx.goodsId, true);

            // 更新安全库存预警状态
            if (querysale.SafetyStockLowNum.ToInt(0) > 0 || querysale.SafetyStockTallNum.ToInt(0) > 0)
            {
                querysale.StockWarning = 0;
                if (querysale.SafetyStockLowNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) < querysale.SafetyStockLowNum.ToInt(0))
                    querysale.StockWarning = 1; // 库存不足

                if (querysale.SafetyStockTallNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) > querysale.SafetyStockTallNum.ToInt(0))
                    querysale.StockWarning = 2; // 库存超额
            }
            else
            {
                querysale.StockWarning = -1;
            }
        }
        else
        {
            querysale.Reject = querysale.Reject.ToInt(0) + redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) + redInkCount;
        }

        // 处理批次信息
        if (goods?.isbatch == true && outboundRecord.WarehouseBatchId.HasValue)
        {
            // 更新批次库存
            var batch = await _repbatch.GetByIdAsync(outboundRecord.WarehouseBatchId.Value);
            if (batch != null)
            {
                if (outboundRecord.GoodProduct == true)
                    batch.GoodProductNum = batch.GoodProductNum.ToInt(0) + redInkCount;
                else
                    batch.RejectNum = batch.RejectNum.ToInt(0) + redInkCount;

                // 更新批次状态
                if (batch.GoodProductNum > 0 || batch.RejectNum > 0)
                {
                    batch.ShelflifeStatus = 0; // 重置批次状态

                    // 添加批次更新队列
                    if (!await _repWarehouseBatchUpdateQueue.IsAnyAsync(x => x.BatchId == batch.Id))
                    {
                        await _repWarehouseBatchUpdateQueue.InsertAsync(new WarehouseBatchUpdateQueue { BatchId = batch.Id });
                    }
                }

                await _repbatch.UpdateAsync(batch);
            }

            // 更新库存的保质期预警状态
            var activeBatches = await _repbatch.AsQueryable()
                .Where(u => u.InventoryId == querysale.Id && u.GoodProductNum > 0)
                .ToListAsync();

            querysale.ExpiredWarning = activeBatches.Any()
                ? activeBatches.Max(x => x.ShelflifeStatus) ?? -1
                : -1;
        }

        // 创建红冲出库记录
        var redInkOutboundRecord = new OutboundRecord
        {
            OrderNum = redInkOrderNum,
            WarehouseOutMxId = outMx.Id,
            OutBoundCount = -redInkCount, // 负数表示红冲
            WarehouseBatchId = outboundRecord.WarehouseBatchId,
            TenantId = _userManager.TenantId,
            GoodProduct = outboundRecord.GoodProduct, // 使用原出库记录的良品次品标识，而不是明细表的
            RedInkStatus = 2, // 标记为红冲记录
            OriginalRecordId = outboundRecordId // 关联原始记录ID
        };

        // 添加备注字段（如果OutboundRecord有此字段）
        redInkOutboundRecord.Remark = $"红冲记录，原记录ID: {outboundRecordId}，产品类型: {(outboundRecord.GoodProduct == true ? "良品" : outboundRecord.GoodProduct == false ? "次品" : "未知")}";

        await _rep.InsertAsync(redInkOutboundRecord);

        // 标记原记录为已被红冲
        outboundRecord.RedInkStatus = 1;
        await _rep.UpdateAsync(outboundRecord);

        // 更新库存
        await _repStore.UpdateAsync(querysale);

        // 更新出库单状态
        var allMxList = await _repmx.GetListAsync(x => x.OutId == outOrder.Id && x.IsDelete == false);
        var totalOutCount = allMxList.Sum(x => x.OutCount.ToInt(0));
        var totalTrueOutCount = allMxList.Sum(x => x.TrueOutCount.ToInt(0));

        if (totalTrueOutCount == 0)
        {
            outOrder.OutboundStatus = 1; // 已提交
        }
        else if (totalTrueOutCount < totalOutCount)
        {
            outOrder.OutboundStatus = 2; // 部分出库
        }
        else
        {
            outOrder.OutboundStatus = 3; // 完全出库
        }

        await _repout.UpdateAsync(outOrder);

        // 更新相关业务单据状态
        if (!string.IsNullOrEmpty(outOrder.SuperiorNum))
        {
            await App.GetRequiredService<WarehouseoutService>().UpdateStatus(outOrder.SuperiorNum, outOrder.OutboundStatus);
        }
    }

    /// <summary>
    /// 入库记录红冲
    /// </summary>
    /// <param name="inboundRecordId">需要红冲的入库记录ID</param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "RedInkInboundRecord")]
    public async Task RedInkInboundRecord([FromQuery] long inboundRecordId)
    {
        // 获取原入库记录
        var inboundRecord = await _repI.GetFirstAsync(x => x.Id == inboundRecordId)
            ?? throw Oops.Oh("未找到入库记录");

        // 验证红冲限制
        // 1. 检查是否已经被红冲过
        if (inboundRecord.RedInkStatus == 1)
            throw Oops.Oh("该入库记录已经被红冲过，不允许再次红冲");

        // 2. 检查本身是否是红冲记录
        if (inboundRecord.RedInkStatus == 2)
            throw Oops.Oh("红冲记录不允许再次红冲");

        // 3. 兼容性检查：通过流水号和数量判断（用于没有RedInkStatus字段的旧数据）
        if (inboundRecord.OrderNum?.Contains("-红冲") == true)
            throw Oops.Oh("红冲记录不允许再次红冲");

        if (inboundRecord.InBoundCount < 0)
            throw Oops.Oh("红冲记录不允许再次红冲");

        // 获取入库明细
        var inMx = await _repInMx.GetFirstAsync(x => x.Id == inboundRecord.WarehouseIncordMxId)
            ?? throw Oops.Oh("未找到对应的入库明细");

        // 获取入库单
        var inOrder = await _repIn.GetFirstAsync(x => x.Id == inMx.InrecordId)
            ?? throw Oops.Oh("未找到对应的入库单");

        // 验证是否可以红冲
        if (inOrder.InhouseStatus != RcvStatusEnum.PartiallyReceived && inOrder.InhouseStatus != RcvStatusEnum.Received)
            throw Oops.Oh("只有部分入库或完全入库的单据才能进行红冲操作");

        // 生成红冲记录编号
        string redInkOrderNum = inboundRecord.OrderNum + "-红冲";

        // 获取库存信息
        var querysale = await _repStore.GetFirstAsync(x => x.TradeID == inMx.GoodsId && x.WarehouseId == inOrder.Warehouseid)
            ?? throw Oops.Oh($"未找到商品ID为{inMx.GoodsId}的库存信息");

        // 获取商品信息
        var goods = await _repGoods.GetByIdAsync(inMx.GoodsId);

        // 红冲数量
        int redInkCount = inboundRecord.InBoundCount.ToInt(0);

        // 入库明细实际入库数量调整（使用RcvQty字段）
        inMx.RcvQty = inMx.RcvQty.ToInt(0) - redInkCount;
        await _repInMx.UpdateAsync(inMx);

        // 处理库存 - 红冲时减少库存
        if (inboundRecord.GoodProduct == true)
        {
            // 检查是否有足够库存
            if (querysale.GoodProduct.ToInt(0) < redInkCount)
                throw Oops.Oh($"商品[{goods?.Name}]库存不足，无法执行红冲操作");

            querysale.GoodProduct = querysale.GoodProduct.ToInt(0) - redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) - redInkCount;

            // 更新安全库存预警状态
            if (querysale.SafetyStockLowNum.ToInt(0) > 0 || querysale.SafetyStockTallNum.ToInt(0) > 0)
            {
                querysale.StockWarning = 0;
                if (querysale.SafetyStockLowNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) < querysale.SafetyStockLowNum.ToInt(0))
                    querysale.StockWarning = 1; // 库存不足

                if (querysale.SafetyStockTallNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) > querysale.SafetyStockTallNum.ToInt(0))
                    querysale.StockWarning = 2; // 库存超额
            }
            else
            {
                querysale.StockWarning = -1;
            }
        }
        else
        {
            // 检查是否有足够库存
            if (querysale.Reject.ToInt(0) < redInkCount)
                throw Oops.Oh($"商品[{goods?.Name}]不良品库存不足，无法执行红冲操作");

            querysale.Reject = querysale.Reject.ToInt(0) - redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) - redInkCount;
        }

        // 重新计算库存相关数据（在途数、待出库数、可配数、缺货状态）
        await RecalculateInventoryData(querysale, inOrder.Warehouseid.Value, inMx.GoodsId, inboundRecord.GoodProduct == true);

        // 处理批次信息
        if (goods?.isbatch == true && inboundRecord.WarehouseBatchId.HasValue)
        {
            // 更新批次库存
            var batch = await _repbatch.GetByIdAsync(inboundRecord.WarehouseBatchId.Value);
            if (batch != null)
            {
                if (inboundRecord.GoodProduct == true)
                {
                    // 检查批次库存是否足够
                    if (batch.GoodProductNum.ToInt(0) < redInkCount)
                        throw Oops.Oh($"商品[{goods.Name}]批次[{batch.Batchnumber}]库存不足，无法执行红冲操作");

                    batch.GoodProductNum = batch.GoodProductNum.ToInt(0) - redInkCount;
                }
                else
                {
                    // 检查批次库存是否足够
                    if (batch.RejectNum.ToInt(0) < redInkCount)
                        throw Oops.Oh($"商品[{goods.Name}]批次[{batch.Batchnumber}]不良品库存不足，无法执行红冲操作");

                    batch.RejectNum = batch.RejectNum.ToInt(0) - redInkCount;
                }

                // 更新批次状态
                if (batch.GoodProductNum <= 0 && batch.RejectNum <= 0)
                {
                    batch.ShelflifeStatus = -1; // 批次库存为空
                    await _repWarehouseBatchUpdateQueue.DeleteAsync(x => x.BatchId == batch.Id);
                }
                else
                {
                    // 添加批次更新队列
                    if (!await _repWarehouseBatchUpdateQueue.IsAnyAsync(x => x.BatchId == batch.Id))
                    {
                        await _repWarehouseBatchUpdateQueue.InsertAsync(new WarehouseBatchUpdateQueue { BatchId = batch.Id });
                    }
                }

                await _repbatch.UpdateAsync(batch);
            }

            // 更新库存的保质期预警状态
            var activeBatches = await _repbatch.AsQueryable()
                .Where(u => u.InventoryId == querysale.Id && u.GoodProductNum > 0)
                .ToListAsync();

            querysale.ExpiredWarning = activeBatches.Any()
                ? activeBatches.Max(x => x.ShelflifeStatus) ?? -1
                : -1;
        }

        // 创建红冲入库记录
        var redInkInboundRecord = new InboundRecord
        {
            OrderNum = redInkOrderNum,
            WarehouseIncordMxId = inMx.Id,
            InBoundCount = -redInkCount, // 负数表示红冲
            WarehouseBatchId = inboundRecord.WarehouseBatchId,
            TenantId = _userManager.TenantId,
            GoodProduct = inboundRecord.GoodProduct, // 使用原入库记录的良品次品标识，而不是明细表的
            Remark = $"红冲记录，原记录ID: {inboundRecordId}，产品类型: {(inboundRecord.GoodProduct == true ? "良品" : inboundRecord.GoodProduct == false ? "次品" : "未知")}",
            RedInkStatus = 2, // 标记为红冲记录
            OriginalRecordId = inboundRecordId // 关联原始记录ID
        };

        await _repI.InsertAsync(redInkInboundRecord);

        // 标记原记录为已被红冲
        inboundRecord.RedInkStatus = 1;
        await _repI.UpdateAsync(inboundRecord);

        // 更新库存
        await _repStore.UpdateAsync(querysale);

        // 更新入库单状态
        var allMxList = await _repInMx.GetListAsync(x => x.InrecordId == inOrder.Id && x.IsDelete == false);

        // 计算总数量和实际入库数量（使用RcvQty作为实际入库数量）
        var totalDocumentNum = allMxList.Sum(x => x.DocumentNum.ToInt(0)); // 单据数量
        var totalRcvQty = allMxList.Sum(x => x.RcvQty.ToInt(0)); // 实际入库数量

        // 根据实际入库情况调整状态
        if (totalRcvQty == 0)
        {
            inOrder.InhouseStatus = RcvStatusEnum.Tobestored; // 待入库
        }
        else if (totalRcvQty < totalDocumentNum)
        {
            inOrder.InhouseStatus = RcvStatusEnum.PartiallyReceived; // 部分入库
        }
        else
        {
            inOrder.InhouseStatus = RcvStatusEnum.Received; // 完全入库
        }

        await _repIn.UpdateAsync(inOrder);
    }

    /// <summary>
    /// 重新计算库存相关数据（在途数、待出库数、可配数、缺货状态）
    /// </summary>
    /// <param name="warehouseStore">库存对象</param>
    /// <param name="warehouseId">仓库ID</param>
    /// <param name="goodsId">商品ID</param>
    /// <param name="isGoodProduct">是否为良品</param>
    /// <returns></returns>
    private async Task RecalculateInventoryData(WarehouseStore warehouseStore, long warehouseId, long goodsId, bool isGoodProduct)
    {
        // 重新计算在途数 - 查询当前商品在该仓库的所有入库明细（包括所有状态）
        var currentInMxList = await _repInMx.AsQueryable()
            .InnerJoin<WarehouseInrecord>((mx, wi) => mx.InrecordId == wi.Id)
            .Where((mx, wi) => wi.Warehouseid == warehouseId 
                               && mx.GoodsId == goodsId 
                               && wi.IsDelete == false) // 查询所有状态的入库单据
            .Select((mx, wi) => new { mx.DocumentNum, mx.RcvQty })
            .ToListAsync();

        // 重新计算在途数 = 所有单据数量 - 所有已入库数量
        warehouseStore.IntransitNum = currentInMxList.Sum(x => x.DocumentNum.ToInt(0)) - currentInMxList.Sum(x => x.RcvQty.ToInt(0));

        // 如果是良品，还需要重新计算可配数
        if (isGoodProduct)
        {
            // 查询当前商品在该仓库的所有出库明细
            var currentOutMxList = await _repmx.AsQueryable()
                .InnerJoin<Warehouseout>((mx, wo) => mx.OutId == wo.Id)
                .Where((mx, wo) => wo.WarehouseId == warehouseId 
                                   && mx.goodsId == goodsId 
                                   && mx.GoodProduct == true
                                   && wo.IsDelete == false 
                                   && (wo.OutboundStatus == 1 || wo.OutboundStatus == 2)) // 待出库或部分出库
                .Select((mx, wo) => new { mx.OutCount, mx.TrueOutCount })
                .ToListAsync();

            // 重新计算待出库数 = 所有出库数量 - 实际出库数量
            warehouseStore.ShippedoutNum = currentOutMxList.Sum(x => x.OutCount.ToInt(0)) - currentOutMxList.Sum(x => x.TrueOutCount.ToInt(0));
            
            // 重新计算可配数 = 良品库存 - 待出库数
            warehouseStore.Compatible = warehouseStore.GoodProduct.ToInt(0) - warehouseStore.ShippedoutNum.ToInt(0);
            warehouseStore.StockOrNot = warehouseStore.Compatible.ToInt(0) < 0;
        }
    }

    /// <summary>
    /// 批量红冲出入库记录
    /// </summary>
    /// <param name="recordIds">记录ID列表</param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "BatchRedInkCancel")]
    public async Task BatchRedInkCancel([FromBody] List<long> recordIds)
    {
        if (recordIds == null || recordIds.Count == 0)
            throw Oops.Oh("请选择要红冲的记录");

        // 获取所有相关记录信息并按库存分组排序，避免死锁
        var recordInfos = new List<dynamic>();

        // 查询出库记录
        var outRecords = await _rep.AsQueryable()
            .LeftJoin<WarehouseoutMX>((r, mx) => r.WarehouseOutMxId == mx.Id)
            .LeftJoin<Warehouseout>((r, mx, wo) => mx.OutId == wo.Id)
            .Where((r, mx, wo) => recordIds.Contains(r.Id))
            .Select((r, mx, wo) => new
            {
                RecordId = r.Id,
                Type = "OutBound",
                WarehouseId = wo.WarehouseId,
                GoodsId = mx.goodsId,
                OrderKey = $"{wo.WarehouseId}_{mx.goodsId}" // 用于排序避免死锁
            })
            .ToListAsync();

        // 查询入库记录
        var inRecords = await _repI.AsQueryable()
            .LeftJoin<WarehouseInrecordMX>((r, mx) => r.WarehouseIncordMxId == mx.Id)
            .LeftJoin<WarehouseInrecord>((r, mx, wi) => mx.InrecordId == wi.Id)
            .Where((r, mx, wi) => recordIds.Contains(r.Id))
            .Select((r, mx, wi) => new
            {
                RecordId = r.Id,
                Type = "InBound",
                WarehouseId = wi.Warehouseid,
                GoodsId = mx.GoodsId,
                OrderKey = $"{wi.Warehouseid}_{mx.GoodsId}" // 用于排序避免死锁
            })
            .ToListAsync();

        // 合并记录并按OrderKey排序，确保同一库存的操作按顺序执行，避免死锁
        var allRecords = outRecords.Cast<dynamic>()
            .Concat(inRecords.Cast<dynamic>())
            .OrderBy(x => x.OrderKey)
            .ThenBy(x => x.RecordId)
            .ToList();

        var successCount = 0;
        var errorMessages = new List<string>();

        // 按库存分组顺序处理，避免死锁
        foreach (var record in allRecords)
        {
            var maxRetries = 3;
            var retryDelay = 100;
            var success = false;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (record.Type == "OutBound")
                    {
                        await RedInkOutboundRecord(record.RecordId);
                    }
                    else
                    {
                        await RedInkInboundRecord(record.RecordId);
                    }
                    successCount++;
                    success = true;
                    break;
                }
                catch (Exception ex) when (ex.Message.Contains("Deadlock") && attempt < maxRetries)
                {
                    // 死锁时重试，等待随机时间避免再次冲突
                    await Task.Delay(retryDelay * attempt + new Random().Next(50, 200));
                }
                catch (Exception ex)
                {
                    var recordType = record.Type == "OutBound" ? "出库" : "入库";
                    errorMessages.Add($"{recordType}记录ID {record.RecordId} 红冲失败: {ex.Message}");
                    break;
                }
            }

            if (!success && !errorMessages.Any(msg => msg.Contains(record.RecordId.ToString())))
            {
                var recordType = record.Type == "OutBound" ? "出库" : "入库";
                errorMessages.Add($"{recordType}记录ID {record.RecordId} 红冲失败: 重试{maxRetries}次后仍然失败");
            }

            // 添加短暂延迟，减少数据库并发压力
            if (success)
            {
                await Task.Delay(50);
            }
        }

        // 返回处理结果
        if (successCount == 0)
        {
            throw Oops.Oh($"批量红冲全部失败。错误详情：{string.Join("; ", errorMessages)}");
        }
        else if (errorMessages.Count > 0)
        {
            throw Oops.Oh($"批量红冲部分成功，成功 {successCount} 条，失败 {errorMessages.Count} 条。失败详情：{string.Join("; ", errorMessages)}");
        }
    }

    public class PrintRecord
    {
        public long Id { get; set; }
        public string Type { get; set; }
    }
}

