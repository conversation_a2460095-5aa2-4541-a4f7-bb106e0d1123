﻿namespace Admin.NET.Application;

    /// <summary>
    /// 出库记录输出参数
    /// </summary>
    public class OutboundRecordOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }

       /// <summary>
       /// 出库明细Id
       /// </summary>
       public long? WarehouseOutMxId { get; set; }

       /// <summary>
       /// 出库数量
       /// </summary>
       public int? OutBoundCount { get; set; }

       /// <summary>
       /// 打印次数
       /// </summary>
       public int? PrintCount { get; set; }

       /// <summary>
       /// 批次Id
       /// </summary>
       public long? WarehouseBatchId { get; set; }

       /// <summary>
       /// 是否良品
       /// </summary>
       public bool? GoodProduct { get; set; }

       /// <summary>
       /// 产品类型描述（良品/次品）
       /// </summary>
       public string ProductTypeDesc => GoodProduct == true ? "良品" : GoodProduct == false ? "次品" : "未知";

    }


