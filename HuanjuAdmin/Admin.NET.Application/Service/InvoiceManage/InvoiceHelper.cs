﻿// MIT License
//
// Copyright (c) 2021-present <PERSON>oh<PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 

using Admin.NET.Application.Service.InvoiceManage.Entity;
using Admin.NET.Core;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.InvoiceManage;
public static class InvoiceHelper
{
    static readonly string IsDebug = App.GetConfig<string>("Invoice:IsDebug");                      //调试模式
    static readonly string TestBaseUrl = App.GetConfig<string>("Invoice:TestBaseUrl");          //测试地址
    static readonly string BaseUrl = App.GetConfig<string>("Invoice:BaseUrl");                          //正式地址
    static readonly string JumpUrl = App.GetConfig<string>("Invoice:JumpUrl");                      //正式地址
    static WebApiUtil webApiUtil = new WebApiUtil();

    /// <summary>
    /// 根据租户信息获取 公钥 私钥
    /// </summary>
    public static SysTenant _sysTenant { get; set; }
    /// <summary>
    /// 机构信息
    /// </summary>
    public static SysOrg _sysOrg { get; set; }
    /// <summary>
    /// 用户信息
    /// </summary>
    public static SysUser _sysUser { get; set; }

    /// <summary>
    /// 获取Token
    /// </summary>
    /// <returns></returns>
    public static string GetToken()
    {
        string baseUrl = BaseUrl + "/gateway/genToken/obtainToken";
        /*
         * sign计算方式：
            1.将所需参数按照这种方式传入值（key=&secret=&timestamp=）
            2.用MD5加密后字母转换为大写（32位）
         * 
         */
        var timestamp = DateTimeToUnixTimestamp(DateTime.Now);
        var sign = $"key={_sysTenant.PublicKey}&secret={_sysTenant.PrivateKey}&timestamp={timestamp}";
        baseUrl += $"?key={_sysTenant.PublicKey}&timestamp={timestamp}&sign={GetMD5_32(sign)}";
        var respose = webApiUtil.GetResponse<ResponseInvoice>(baseUrl);
        if (respose.code == 200)
        {
            return JsonConvert.DeserializeObject<TokenInfo>(respose.data).token;
        }
        else
        {
            var errorMsg = $"code:{respose.code};message:{respose.message}";
            return errorMsg;
        }
    }
    /// <summary>
    /// 登录电票平台
    /// </summary>
    /// <returns></returns>
    public static RepairInvoiceResponse LoginDppt(LoginDpptRequest loginDpptRequest)
    {
        var token = GetToken();
        string baseUrl = "https://open.gateway.newtimeai.com/qdjk/fullExteriorInvoke/loginDppt";
        var timestamp = DateTimeToUnixTimestamp(DateTime.Now);
        var sign = GetMD5_32($"timestamp={timestamp}&token={token}&url=/qdjk/fullExteriorInvoke/loginDppt");

        Dictionary<string, string> headers = new Dictionary<string, string>();
        headers.Add("timestamp", timestamp + "");
        headers.Add("sign", sign);
        headers.Add("token", token);
        var postData = JsonConvert.SerializeObject(loginDpptRequest);
        var respose = webApiUtil.HttpPostByForm<RepairInvoiceResponse>(baseUrl, postData, headers);
        return new RepairInvoiceResponse() { code = respose.code, message = respose.message };
        //if (respose.code == 200)
        //{
        //    //return JsonConvert.DeserializeObject<AsyncInvoiceResponse>(respose.data);
        //    return new RepairInvoiceResponse() { code = 200 };
        //}
        //else
        //{
        //    return new RepairInvoiceResponse() { code = respose.code, message = respose.message };
        //}
    }
    /// <summary>
    /// 查询可修复的发票数据
    /// </summary>
    /// <returns></returns>
    public static AsyncInvoiceResponse GetFaPiaoXX(AsyncInvoiceRequest asyncInvoiceRequest, int IsValidate)
    {
        var token = GetToken();
        string baseUrl = IsDebug == "1" ? TestBaseUrl + "/qdjk/fullExteriorInvoke/invoiceQuery" : BaseUrl + "/qdjk/fullExteriorInvoke/invoiceQuery";
        var timestamp = DateTimeToUnixTimestamp(DateTime.Now);
        var sign = GetMD5_32($"timestamp={timestamp}&token={token}&url=/qdjk/fullExteriorInvoke/invoiceQuery");

        Dictionary<string, string> headers = new Dictionary<string, string>();
        headers.Add("timestamp", timestamp + "");
        headers.Add("sign", sign);
        headers.Add("token", token);

        var postData = JsonConvert.SerializeObject(asyncInvoiceRequest);
        var respose = webApiUtil.HttpPostByForm<AsyncInvoiceResponse>(baseUrl, postData, headers);
        if (respose.code == 302)//请您获取验证码重新登录
        {
            if (IsValidate == 1)
                return new AsyncInvoiceResponse() { code = 302, message = "请发送验证码" };
            else
            {
                LoginDpptRequest loginDpptRequest = new LoginDpptRequest();
                loginDpptRequest.nsrsbh = _sysOrg.TaxId;
                loginDpptRequest.username = _sysUser.DPUserName;
                loginDpptRequest.password = _sysUser.DPPassword;
                var loginResult = LoginDppt(loginDpptRequest);
                if (loginResult.code == 200)
                    respose = webApiUtil.HttpPostByForm<AsyncInvoiceResponse>(baseUrl, postData, headers);
                else
                    return new AsyncInvoiceResponse() { code = loginResult.code, message = loginResult.message };
            }
        }
        return respose;
        //if (respose.code == 200)
        //{
        //    return JsonConvert.DeserializeObject<AsyncInvoiceResponse>(respose.data.kpxx.group);
        //    //return "";
        //}
        //else
        //{
        //    var errorMsg = $"code:{respose.code};message:{respose.message}";
        //    return errorMsg;
        //}
    }
    /// <summary>
    /// 修复发票
    /// </summary>
    /// <returns></returns>
    public static ResponseInvoice FaPiaoXX(RepairInvoiceRequest repairInvoiceRequest)
    {
        var token = GetToken();
        string baseUrl = IsDebug == "1" ? TestBaseUrl + "/qdjk/fullExteriorInvoke/invoiceRepair" : BaseUrl + "/qdjk/fullExteriorInvoke/invoiceRepair";
        //string baseUrl = "https://open.gateway.newtimeai.com/qdjk/fullExteriorInvoke/invoiceRepair";
        var timestamp = DateTimeToUnixTimestamp(DateTime.Now);
        var sign = GetMD5_32($"timestamp={timestamp}&token={token}&url=/qdjk/fullExteriorInvoke/invoiceRepair");

        Dictionary<string, string> headers = new Dictionary<string, string>();
        headers.Add("timestamp", timestamp + "");
        headers.Add("sign", sign);
        headers.Add("token", token);

        var postData = JsonConvert.SerializeObject(repairInvoiceRequest);
        var respose = webApiUtil.PostResponse<ResponseInvoice>(baseUrl, postData, headers);
        return respose;
    }

    /// <summary>
    /// 下载发票
    /// </summary>
    /// <returns></returns>
    public static DownloadInvoiceResponse InvoiceDownload(InvoiceDownloadRequestDetail currentFPXX)
    {
        var token = GetToken();
        var timestamp = DateTimeToUnixTimestamp(DateTime.Now);

        string baseUrl = "", sign = "";
        if (currentFPXX.natureInvoice == 0)
        {
            //销项发票下载接口
            baseUrl = IsDebug == "1" ? TestBaseUrl + "/qdjk/fullExteriorInvoke/getInvoicePdfOfd" : BaseUrl + "/qdjk/fullExteriorInvoke/getInvoicePdfOfd";
            //baseUrl = "https://open.gateway.newtimeai.com/qdjk/fullExteriorInvoke/getInvoicePdfOfd";
            sign = GetMD5_32($"timestamp={timestamp}&token={token}&url=/qdjk/fullExteriorInvoke/getInvoicePdfOfd");
        }
        else
        {
            //进项发票下载接口
            baseUrl = IsDebug == "1" ? TestBaseUrl + "/qdjk/fullExteriorInvoke/getJxFile" : BaseUrl + "/qdjk/fullExteriorInvoke/getJxFile";
            //baseUrl = "https://open.gateway.newtimeai.com/qdjk/fullExteriorInvoke/getJxFile";
            sign = GetMD5_32($"timestamp={timestamp}&token={token}&url=/qdjk/fullExteriorInvoke/getJxFile");
        }

        Dictionary<string, string> headers = new Dictionary<string, string>();
        headers.Add("timestamp", timestamp + "");
        headers.Add("sign", sign);
        headers.Add("token", token);

        var postData = JsonConvert.SerializeObject(currentFPXX);
        var respose = webApiUtil.HttpPostByForm<DownloadInvoiceResponse>(baseUrl, postData, headers);
        return respose;
    }

    /// <summary>
    /// 全电蓝票
    /// </summary>
    /// <returns></returns>
    public static string QuanDianLP()
    {
        //先获取Token
        string token = GetToken();

        return "";
    }

    /// <summary>
    /// 日期转换成unix时间戳（毫秒）
    /// </summary>
    /// <param name="dateTime"></param>
    /// <returns></returns>
    public static long DateTimeToUnixTimestamp(DateTime dateTime)
    {
        var start = new DateTime(1970, 1, 1, 0, 0, 0, dateTime.Kind);
        return Convert.ToInt64((dateTime - start).TotalMilliseconds);
    }

    /// <summary>
    /// 32位MD5加密
    /// </summary>
    /// <param name="password"></param>
    /// <returns></returns>
    public static string MD5Encrypt32(string password)
    {
        string cl = password;
        string pwd = "";
        MD5 md5 = MD5.Create(); //实例化一个md5对像
                                // 加密后是一个字节类型的数组，这里要注意编码UTF8/Unicode等的选择　
        byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(cl));
        // 通过使用循环，将字节类型的数组转换为字符串，此字符串是常规字符格式化所得
        for (int i = 0; i < s.Length; i++)
        {
            // 将得到的字符串使用十六进制类型格式。格式后的字符是小写的字母，如果使用大写（X）则格式后的字符是大写字符 
            pwd = pwd + s[i].ToString("X");
        }
        return pwd;
    }
    public static string GetMD5_32(string password)
    {
        MD5 md5 = MD5.Create();
        byte[] t = md5.ComputeHash(Encoding.UTF8.GetBytes(password));
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < t.Length; i++)
        {
            stringBuilder.Append(t[i].ToString("x").PadLeft(2, '0'));
        }
        return stringBuilder.ToString().ToUpper();
    }

    /// <summary>
    /// 获取Token
    /// </summary>
    /// <returns></returns>
    public static string GetTokenJump(string zyczydm)
    {
        string jumpUrl = JumpUrl + "/cloudInventory/createZyToken";
        string postData = $"{{\"zyczydm\":\"{zyczydm}\"}}";
        // 创建自定义请求头字典
        var customHeaders = new Dictionary<string, string>
        {
            { "Request-End", "hjkj" }
        };
        var respose = webApiUtil.PostResponse<ResponseInvoice>(jumpUrl, postData, customHeaders);
        if (respose.code == 200)
        {
            return respose.data;
        }
        else
        {
            var errorMsg = $"code:{respose.code};message:{respose.message}";
            return errorMsg;
        }
    }

    /// <summary>
    /// 添加商品
    /// </summary>
    public static string CreateGoodsInfo(CreateInvoiceRequest cir, string token, string fwqid)
    {
        string jumpUrl = JumpUrl + "/cloudInventory/createCloudInventoryInvoice";
        string postData = JsonConvert.SerializeObject(cir);
        // 创建自定义请求头字典
        var customHeaders = new Dictionary<string, string>
        {
            { "Request-End", "hjkj" },
            { "token", token},
            { "Fwqid", fwqid}
        };
        var respose = webApiUtil.PostResponse<JumpApiResponse<CreateInvoiceResponse>>(jumpUrl, postData, customHeaders);
        if (respose.Code == 200)
        {
            return respose.Data.DocumentNumber;
        }
        else
        {
            var errorMsg = $"code:{respose.Code};message:{respose.Message}";
            return errorMsg;
        }
    }

    /// <summary>
    /// 根据纳税人识别号查询ID
    /// </summary>
    public static string QueryZyidByNsrsbh(string nsrsbh, string token)
    {
        string jumpUrl = JumpUrl + "/cloudInventory/queryZyidByNsrsbh";
        // 构建 JSON 字符串
        string postData = "{\"nsrsbh\": \"" + nsrsbh + "\"}";
        // 创建自定义请求头字典
        var customHeaders = new Dictionary<string, string>
        {
            { "Request-End", "hjkj" },
            { "token", token},
            { "sybs", "B"}
        };
        var respose = webApiUtil.PostResponse<ResponseInvoice>(jumpUrl, postData, customHeaders);
        if (respose.code == 200)
        {
            return respose.data;
        }
        else
        {
            var errorMsg = $"code:{respose.code};message:{respose.message}";
            return errorMsg;
        }
    }
}
