﻿using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.InvoiceManage.Entity;
using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 发票管理基础输入参数
/// </summary>
public class InvoiceManageBaseInput
{
    /// <summary>
    /// 时间
    /// </summary>
    public virtual DateTime? Time { get; set; }

    /// <summary>
    /// 发票性质
    /// </summary>
    public virtual int? NatureInvoice { get; set; }

    /// <summary>
    /// 发票时间
    /// </summary>
    public virtual DateTime? InvoiceDate { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    public virtual long? CustomId { get; set; }

    /// <summary>
    /// 来往单位
    /// </summary>
    public virtual string? ComeUnit { get; set; }

    /// <summary>
    /// 税务登记号
    /// </summary>
    public virtual string? TaxId { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    public virtual string? BankName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public virtual string? BankCode { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public virtual string? Address { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public virtual string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public virtual string? Phone { get; set; }

    /// <summary>
    /// 发票号码
    /// </summary>
    public virtual string? InvoiceNum { get; set; }

    /// <summary>
    /// 未税金额
    /// </summary>
    public virtual decimal? UntaxedMoney { get; set; }

    /// <summary>
    /// 税额
    /// </summary>
    public virtual decimal? TaxAmount { get; set; }

    /// <summary>
    /// 发票金额
    /// </summary>
    public virtual decimal? InvoiceValue { get; set; }

    /// <summary>
    /// 税率
    /// </summary>
    public virtual string? TaxRate { get; set; }

    /// <summary>
    /// 收款单号
    /// </summary>
    public virtual string? ReceiptNum { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public virtual int? InvoiceStatus { get; set; }

    /// <summary>
    /// 公钥
    /// </summary>
    public virtual string? PublicKey { get; set; }

    /// <summary>
    /// 私钥
    /// </summary>
    public virtual string? PrivateKey { get; set; }
    /// <summary>
    /// 发票种类 蓝票0，红票1
    /// </summary>
    public virtual int? InvoiceType { get; set; }
    /// <summary>
    /// 发票类型 普票0，专票1
    /// </summary>
    public virtual int? InvoiceTypeLx { get; set; }

}

/// <summary>
/// 发票管理分页查询输入参数
/// </summary>
public class InvoiceManageInput : BasePageInput
{
    /// <summary>
    /// 时间
    /// </summary>
    public DateTime? Time { get; set; }

    /// <summary>
    /// 时间范围
    /// </summary>
    public List<DateTime?> TimeRange { get; set; }
    /// <summary>
    /// 发票号码
    /// </summary>
    public string? InvoiceNum { get; set; }

    /// <summary>
    /// 收款单号
    /// </summary>
    public string? ReceiptNum { get; set; }

    /// <summary>
    /// 来往单位
    /// </summary>
    public string? ComeUnit { get; set; }

    /// <summary>
    /// 发票性质
    /// </summary>
    public int? NatureInvoice { get; set; }
    /// <summary>
    /// 发票种类 蓝票0，红票1
    /// </summary>
    public int? InvoiceType { get; set; }
    /// <summary>
    /// 发票类型 普票0，专票1
    /// </summary>
    public int? InvoiceTypeLx { get; set; }

}

/// <summary>
/// 发票管理增加输入参数
/// </summary>
public class AddInvoiceManageInput : InvoiceManageBaseInput
{
}

/// <summary>
/// 发票管理增加输入参数
/// </summary>
public class AddInvoiceManageInputWithMX
{
    public List<AddInvoiceManageInput> addInvoiceManageInput { get; set; }
    public List<AddInvoiceManageDetailInput> listMx { get; set; }
}

/// <summary>
/// 发票管理删除输入参数
/// </summary>
public class DeleteInvoiceManageInput : BaseIdInput
{
}

/// <summary>
/// 发票管理更新输入参数
/// </summary>
public class UpdateInvoiceManageInput : InvoiceManage
{
}

/// <summary>
/// 发票管理主键查询输入参数
/// </summary>
public class QueryByIdInvoiceManageInput : DeleteInvoiceManageInput
{

}
/// <summary>
/// 发票同步输入参数
/// </summary>
public class InvoicePullInput
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }
    /// <summary>
    /// 普票0，专票1
    /// </summary>
    public int InvoiceTypeLx { get; set; }
    /// <summary>
    /// 发票性质 销项发票0，进项发票1
    /// </summary>
    public int NatureInvoice { get; set; }
    /// <summary>
    /// 税区验证 是1否0需要验证
    /// </summary>
    public int IsValidate { get; set; }
   
}

/// <summary>
/// 登录
/// </summary>
public class InvoiceLoginInput
{
    /// <summary>
    /// 验证码
    /// </summary>
    public string Captcha { get; set; }
}
/// <summary>
/// 发票下载输入参数
/// </summary>
public class InvoiceDownloadRequest
{
    /// <summary>
    /// 发票信息
    /// </summary>
    public List<InvoiceDownloadRequestDetail> listFPXX { get; set; }
    
    /// <summary>
    /// 验证码
    /// </summary>
    public string Captcha { get; set; }
}

public class InvoiceDownloadRequestDetail
{
    /// <summary>
    /// 发票号码
    /// </summary>
    public string fphm { get; set; }

    /// <summary>
    /// 发票性质 销项发票0，进项发票1
    /// </summary>
    public int? natureInvoice { get; set; }

    /// <summary>
    /// 获取版式类型 4：下载地址
    /// </summary>
    public string downflag { get; set; }
    /// <summary>
    /// 纳税人识别号
    /// </summary>
    public string nsrsbh { get; set; }
    /// <summary>
    /// 用户电票平台账号（不传使用管理端平台默认维护的账号）
    /// </summary>
    public string username { get; set; }
    /// <summary>
    /// 开票日期（时间戳 非必填）
    /// </summary>
    public DateTime? kprq { get; set; }
}


/// <summary>
/// 发票下载输出参数
/// </summary>
public class InvoiceDownloadResponse
{
    public string ofdUrl { get; set; }
    public string pdfUrl { get; set; }
    public string xmlUrl { get; set; }
}

/// <summary>
/// 开票跳转输入
/// </summary>
public class JumpInvoiceInput
{
    /// <summary>
    /// 收款单ID
    /// </summary>
    public List<long> listIds { get; set; }

    /// <summary>
    /// 发票类型
    /// </summary>
    public string invoiceType { get; set; }
}

