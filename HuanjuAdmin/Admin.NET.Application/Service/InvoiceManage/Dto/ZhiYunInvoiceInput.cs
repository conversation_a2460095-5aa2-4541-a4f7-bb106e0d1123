﻿// MIT License
//
// Copyright (c) 2021-present <PERSON>oh<PERSON>ijun, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using NPOI.HPSF;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.InvoiceManage.Dto;

public class ZhiYunInvoiceInput
{
    public string czydm { get; set; }
    public string dqsj { get; set; }
    public string finalJym { get; set; }
    public decimal kce { get; set; }
    public decimal hjje { get; set; }
    public string ghdwdzdh { get; set; }
    public string sfwzzfp { get; set; }
    public string fpqqlsh { get; set; }
    public string yfphm { get; set; }
    public decimal se { get; set; }
    public string ghdwdm { get; set; }
    public string kprq { get; set; }
    public string bz { get; set; }
    public string zzfphm { get; set; }
    public string id { get; set; }
    public string skr { get; set; }
    public Mxzb[] mxzb { get; set; }
    public Mxzb[] zbmx { get; set; }
    public string ghdwmc { get; set; }
    public string xhdwdm { get; set; }
    public string fpdm { get; set; }
    public string ip { get; set; }
    public string xhdwdzdh { get; set; }
    public int natureInvoice { get; set; }
    public string kpr { get; set; }
    public string fhr { get; set; }
    public string kpdwdm { get; set; }
    public string fplxdm { get; set; }
    public string fpzt { get; set; }
    public string xhdwmc { get; set; }
    public string ghdwyhzh { get; set; }
    public decimal jshj { get; set; }
    public string xfmc { get; set; }
    public object[] tdys { get; set; }
    public string zyspmc { get; set; }
    public string xfbz { get; set; }
    public string fphm { get; set; }
    public string jym { get; set; }
    public string xhdwyhzh { get; set; }
    public string zsfs { get; set; }
    public string tscbz { get; set; }
    public string gmfjbr { get; set; }
    public string tqm { get; set; }
    public string skm { get; set; }
    public string yfpdm { get; set; }
    public string scbz { get; set; }
    public string qdbz { get; set; }
    public string gFKHDH { get; set; }
    public string dfgtgmbq { get; set; }
    public string zfrdm { get; set; }
    public string bmbbbh { get; set; }
    public string qmcs { get; set; }
    public string gFKHYX { get; set; }
    public string jbrzrrnsrsbh { get; set; }
    public string zffs { get; set; }
    public string qmbz { get; set; }
    public string slsm { get; set; }
    public string tdyslxDm { get; set; }
    public string zfr { get; set; }
    public string yqbz { get; set; }
    public string hssign { get; set; }
    public string zfrq { get; set; }
    public string cktslxDm { get; set; }
    public string dZDH { get; set; }
    public string sccgsj { get; set; }
    public string dxtsbz { get; set; }
    public string qmz { get; set; }
    public string jbrsfzjhm { get; set; }
    public string scbsbz { get; set; }
    public string jbrsfzjlx { get; set; }
    public string jbrgjlx { get; set; }
    public string kpddm { get; set; }
    public string sfsdts { get; set; }
    public string zfyy { get; set; }
    public string jqbh { get; set; }
    public string tspz { get; set; }
    public string tzdbh { get; set; }
}

public class Mxzb
{
    public string ggxh { get; set; }
    public string xhdwdm { get; set; }
    public string fpdm { get; set; }
    public float spsl { get; set; }
    public string yhzcbs { get; set; }
    public string spmc { get; set; }
    public string fpmxxh { get; set; }
    public string fphxz { get; set; }
    public string kpdwdm { get; set; }
    public string hsbz { get; set; }
    public string se { get; set; }
    public string dw { get; set; }
    public string mxid { get; set; }
    public string kprq { get; set; }
    public float spdj { get; set; }
    public string sl { get; set; }
    public string id { get; set; }
    public string je { get; set; }
    public string spbm { get; set; }
    public string fphm { get; set; }
    public string bb { get; set; }
    public string spsm { get; set; }
    public decimal hsdj { get; set; }
    public string zzstsgl { get; set; }
    public string zxbm { get; set; }
    public string lslbs { get; set; }
    public decimal hsje { get; set; }
    public string jqbh { get; set; }
}