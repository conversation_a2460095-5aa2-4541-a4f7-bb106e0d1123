﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.InvoiceManage;
using Admin.NET.Application.Service.InvoiceManage.Dto;
using Admin.NET.Application.Service.InvoiceManage.Entity;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Admin.NET.Core.Util;
using AngleSharp.Dom;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Lazy.Captcha.Core;
using Newtonsoft.Json;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;

namespace Admin.NET.Application;
/// <summary>
/// 发票管理服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class InvoiceManageService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<InvoiceManage> _rep;
    private readonly SqlSugarRepository<InvoiceManageDetail> _repmx;
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<Receipt> _repReceipt;
    private readonly SqlSugarRepository<Salescontract> _repSalescontract;
    private readonly SqlSugarRepository<SaleOfGoods> _saleOfGoods;
    private readonly SqlSugarRepository<Pubcustom> _pubCustom;
    private readonly SqlSugarRepository<InvoicingRecord> _repInvoRecord;
    private readonly SqlSugarRepository<SysUser> _sysUserRep;


    public InvoiceManageService(SqlSugarRepository<InvoiceManage> rep, SqlSugarRepository<InvoiceManageDetail> repmx, UserManager userManager, SqlSugarRepository<Receipt> repReceipt, SqlSugarRepository<SaleOfGoods> saleOfGoods, SqlSugarRepository<Pubcustom> pubCustom, SqlSugarRepository<Salescontract> repSalescontract, SqlSugarRepository<InvoicingRecord> repInvoRecord, SqlSugarRepository<SysUser> sysUserRep)
    {
        _rep = rep;
        _repmx = repmx;
        _userManager = userManager;
        _repReceipt = repReceipt;
        _saleOfGoods = saleOfGoods;
        _pubCustom = pubCustom;
        _repSalescontract = repSalescontract;
        _repInvoRecord = repInvoRecord;
        _sysUserRep = sysUserRep;
    }

    /// <summary>
    /// 分页查询发票管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<InvoiceManageOutput>> Page(InvoiceManageInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.InvoiceNum), u => u.InvoiceNum.Contains(input.InvoiceNum.Trim()))
                    //.WhereIF(!string.IsNullOrWhiteSpace(input.ReceiptNum), u => u.ReceiptNum.Contains(input.ReceiptNum.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.ComeUnit), u => u.ComeUnit.Contains(input.ComeUnit.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.ComeUnit), u => u.ComeUnit.Contains(input.ComeUnit.Trim()))
                    .WhereIF(input.NatureInvoice != null, u => u.NatureInvoice == input.NatureInvoice)
                    .WhereIF(input.InvoiceType != null, u => u.InvoiceType == input.InvoiceType)
                    .WhereIF(input.InvoiceTypeLx != null, u => u.InvoiceTypeLx == input.InvoiceTypeLx)
                    .Select<InvoiceManageOutput>()
;
        if (input.TimeRange != null && input.TimeRange.Count > 0)
        {
            DateTime? start = input.TimeRange[0];
            query = query.WhereIF(start.HasValue, u => u.Time > start);
            if (input.TimeRange.Count > 1 && input.TimeRange[1].HasValue)
            {
                var end = input.TimeRange[1].Value.AddDays(1);
                query = query.Where(u => u.Time < end);
            }
        }
        query = query.OrderBuilder(input, "InvoiceDate");
        var fp = await query.ToPagedListAsync(input.Page, input.PageSize);
        //foreach (var item in fp.Items)
        //    item.listMx = _repmx.AsQueryable().Where(s => s.InvoiceManageId == item.Id).Select<AddInvoiceManageDetailInput>().ToList();
        return fp;
    }

    /// <summary>
    /// 增加发票管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddInvoiceManageInputWithMX input)
    {
        var entity = input.addInvoiceManageInput.Adapt<List<InvoiceManage>>();
        var listMX = input.listMx.Adapt<List<InvoiceManageDetail>>();

        var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
        //if (addSuccess)
        //{
        //    listMX.ForEach(u => u.InvoiceManageId = entity.Id);
        //}

        //if (listMX != null)
        //{
        //    await App.GetRequiredService<InvoiceManageDetailService>().AddOrUpdate(listMX);
        //}

        if (InvoiceHelper._sysTenant == null)
        {
            var sysTenant = await App.GetRequiredService<SysTenantService>().GetTenantDetail(_userManager.TenantId);
            InvoiceHelper._sysTenant = sysTenant;
        }

        InvoiceHelper.QuanDianLP();
    }

    /// <summary>
    /// 删除发票管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteInvoiceManageInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新发票管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateInvoiceManageInput input)
    {
        var entity = input.Adapt<InvoiceManage>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取发票管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<InvoiceManage> Get([FromQuery] QueryByIdInvoiceManageInput input)
    //{
    //}

    /// <summary>
    /// 获取发票管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<InvoiceManageOutput>> List([FromQuery] InvoiceManageInput input)
    {
        return await _rep.AsQueryable().Select<InvoiceManageOutput>().ToListAsync();
    }


    /// <summary>
    /// 同步发票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Pull")]
    public async Task<PullOutPut> Pull(InvoicePullInput input)
    {
        var sysOrg = await App.GetRequiredService<SysOrgService>().GetSysOrgDetail(_userManager.TenantId);
        InvoiceHelper._sysOrg = sysOrg;
        var sysUser = await App.GetRequiredService<SysUserService>().GetBaseInfo();
        InvoiceHelper._sysUser = sysUser;
        var sysTenant = await App.GetRequiredService<SysTenantService>().GetTenantDetail(_userManager.TenantId);
        InvoiceHelper._sysTenant = sysTenant;
        InvoiceHelper._sysTenant.PublicKey = "5dd81a0dee422dc";
        InvoiceHelper._sysTenant.PrivateKey = "7510A4069B2D1C8229C10AFB6";
        if (string.IsNullOrEmpty(InvoiceHelper._sysOrg.TaxId) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPUserName) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPPassword) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PublicKey) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PrivateKey)
            )
            return new PullOutPut() { code = 306, message = "需维护" };

        AsyncInvoiceRequest asyncInvoiceRequest = new AsyncInvoiceRequest();
        asyncInvoiceRequest.username = sysUser.DPUserName;
        asyncInvoiceRequest.nsrsbh = InvoiceHelper._sysOrg.TaxId;
        asyncInvoiceRequest.qqsj = input.StartTime.ToString("yyyyMMdd");
        asyncInvoiceRequest.zzsj = input.EndTime.ToString("yyyyMMdd");
        asyncInvoiceRequest.fplxdm = input.InvoiceTypeLx == 1 ? "81" : "82";// 81专票"82"普;
        asyncInvoiceRequest.page = "1";
        asyncInvoiceRequest.size = "100";
        asyncInvoiceRequest.gjbq = input.NatureInvoice == 0 ? "" : "1";//销项发票0，进项发票1;
        var result = InvoiceHelper.GetFaPiaoXX(asyncInvoiceRequest, input.IsValidate);
        int skipNum = 0;
        if (result.code == 200)
        {
            RepairInvoiceRequest repairInvoiceRequest = new RepairInvoiceRequest();
            repairInvoiceRequest.username = sysUser.DPUserName;
            repairInvoiceRequest.nsrsbh = InvoiceHelper._sysOrg.TaxId;
            repairInvoiceRequest.bs = "1";
            repairInvoiceRequest.fplxdm = input.InvoiceTypeLx == 1 ? "81" : "82";// 81专票"82"普;
            repairInvoiceRequest.qqsj = input.StartTime.ToString("yyyyMMdd");
            repairInvoiceRequest.zzsj = input.EndTime.ToString("yyyyMMdd");
            repairInvoiceRequest.gjbq = input.NatureInvoice == 0 ? "" : "1";//销项发票0，进项发票1;
            repairInvoiceRequest.callBackUrl = "";
            var xxResult = InvoiceHelper.FaPiaoXX(repairInvoiceRequest);
            if (xxResult.code != 200)
                return new PullOutPut() { code = xxResult.code, message = xxResult.message };
            List<InvoiceManage> invoiceList = new List<InvoiceManage>();
            foreach (var item in result.data.kpxx.group)
            {
                var isExist = await _rep.IsAnyAsync(u => u.InvoiceNum == item.fphm);
                if (isExist)
                {
                    skipNum++;
                    continue;
                }
                InvoiceManage invoice = new InvoiceManage();
                invoice.Time = DateTime.Now;
                invoice.NatureInvoice = input.NatureInvoice;
                invoice.InvoiceDate = item.kprq;
                invoice.ComeUnit = input.NatureInvoice == 0 ? item.gfkpmc : item.xfkpmc;
                invoice.TaxId = item.xfkpsh;
                invoice.Contacts = item.kpr;
                invoice.InvoiceNum = item.fphm;
                invoice.UntaxedMoney = item.hjje;//100
                invoice.TaxAmount = item.hjse;//1
                invoice.InvoiceValue = item.jshj;//99
                invoice.InvoiceType = item.fpzt == "00" ? 0 : 1;//00是蓝票，01是红票
                invoice.InvoiceTypeLx = input.InvoiceTypeLx;//00是蓝票，01是红票
                invoiceList.Add(invoice);
            }
            if (invoiceList.Count > 0)
                await _rep.AsInsertable(invoiceList).ExecuteCommandIdentityIntoEntityAsync();
            return new PullOutPut() { code = 200, message = $"同步成功{invoiceList.Count}条,重复跳过{skipNum}条" };
        }
        return new PullOutPut() { code = result.code, message = result.message };
    }

    /// <summary>
    /// 同步发票(智云推送发票信息)
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "SyncInvoices")]
    public async Task<PullOutPut> SyncInvoices(List<ZhiYunInvoiceInput> listZyInvoiceInput)
    {
        int successCount = 0;
        foreach (var item in listZyInvoiceInput)
        {
            var isContained = await _rep.IsAnyAsync(x => x.InvoiceNum == item.fphm);
            if (isContained)
                continue;
            var invoice = new InvoiceManage();
            invoice.Time = item.dqsj?.ToDateTime();
            invoice.NatureInvoice = item.natureInvoice;
            invoice.InvoiceDate = item.kprq?.ToDateTime();
            Mxzb[] arrMxzb = new Mxzb[] { };
            if (item.natureInvoice == 0)
            {
                invoice.ComeUnit = item.ghdwmc;
                invoice.TaxId = item.ghdwdm;
                arrMxzb = item.mxzb;
            }
            else if (item.natureInvoice == 1)
            {
                invoice.ComeUnit = item.xfmc;
                invoice.TaxId = item.kpdwdm;
                arrMxzb = item.zbmx;
            }
            invoice.UntaxedMoney = item.hjje;
            invoice.TaxAmount = item.se;
            invoice.InvoiceValue = item.jshj;

            invoice.InvoiceType = item.fpzt == "00" ? 0 : 1; // 00蓝票 01红票
            invoice.InvoiceTypeLx = item.fplxdm == "81" ? 0 : 1;// 81专票 82普票
            invoice.InvoiceNum = item.fphm;
            invoice.InvoiceAddress = item.jym;
            invoice.Contacts = item.kpr;

            await _rep.InsertReturnIdentityAsync(invoice);
            if (invoice.Id > 0 && arrMxzb.Length > 0)
            {
                var listInvoiceD = new List<InvoiceManageDetail>();
                foreach (var mxzb in arrMxzb)
                {
                    var invoiceD = new InvoiceManageDetail();
                    invoiceD.InvoiceManageId = invoice.Id;
                    invoiceD.GoodsName = mxzb.spmc;
                    invoiceD.SalesNum = mxzb.spsl.ToInt(0);
                    invoiceD.Amount = mxzb.je.ToDecimal(0);
                    invoiceD.TaxRate = mxzb.sl.ToDecimal(0);
                    invoiceD.Tax = mxzb.se.ToDecimal(0);
                    invoiceD.TaxTag = mxzb.hsbz.ToInt(0);
                    listInvoiceD.Add(invoiceD);
                }

                if (listInvoiceD.Count > 0)
                {
                    await _repmx.InsertRangeAsync(listInvoiceD);
                    //主表的Taxrate取子表Taxrate用;分隔
                    var taxRate = listInvoiceD.Select(x => x.TaxRate).ToList();
                    invoice.TaxRate = string.Join(";", taxRate);
                    await _rep.UpdateAsync(invoice);
                }
            }

            successCount++;
        }
        return new PullOutPut() { code = 200, message = $"同步成功{successCount}条" };
    }

    /// <summary>
    /// 发送验证码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "SendCaptcha")]
    public async Task<PullOutPut> SendCaptcha()
    {
        var sysOrg = await App.GetRequiredService<SysOrgService>().GetSysOrgDetail(_userManager.TenantId);
        InvoiceHelper._sysOrg = sysOrg;
        var sysUser = await App.GetRequiredService<SysUserService>().GetBaseInfo();
        InvoiceHelper._sysUser = sysUser;
        var sysTenant = await App.GetRequiredService<SysTenantService>().GetTenantDetail(_userManager.TenantId);
        InvoiceHelper._sysTenant = sysTenant;
        InvoiceHelper._sysTenant.PublicKey = "5dd81a0dee422dc";
        InvoiceHelper._sysTenant.PrivateKey = "7510A4069B2D1C8229C10AFB6";
        if (string.IsNullOrEmpty(InvoiceHelper._sysOrg.TaxId) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPUserName) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPPassword) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PublicKey) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PrivateKey)
            )
            return new PullOutPut() { code = 306, message = "需维护" };
        LoginDpptRequest loginDpptRequest = new LoginDpptRequest();
        loginDpptRequest.nsrsbh = sysOrg.TaxId;
        loginDpptRequest.username = sysUser.DPUserName;
        loginDpptRequest.password = sysUser.DPPassword;
        var loginResult = InvoiceHelper.LoginDppt(loginDpptRequest);
        return new PullOutPut() { code = loginResult.code, message = loginResult.message };
    }
    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "LoginDppt")]
    public async Task<PullOutPut> LoginDppt(InvoiceLoginInput input)
    {
        var sysOrg = await App.GetRequiredService<SysOrgService>().GetSysOrgDetail(_userManager.TenantId);
        InvoiceHelper._sysOrg = sysOrg;
        var sysUser = await App.GetRequiredService<SysUserService>().GetBaseInfo();
        InvoiceHelper._sysUser = sysUser;
        var sysTenant = await App.GetRequiredService<SysTenantService>().GetTenantDetail(_userManager.TenantId);
        InvoiceHelper._sysTenant = sysTenant;
        InvoiceHelper._sysTenant.PublicKey = "5dd81a0dee422dc";
        InvoiceHelper._sysTenant.PrivateKey = "7510A4069B2D1C8229C10AFB6";
        if (string.IsNullOrEmpty(InvoiceHelper._sysOrg.TaxId) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPUserName) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPPassword) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PublicKey) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PrivateKey)
            )
            return new PullOutPut() { code = 306, message = "需维护" };
        LoginDpptRequest loginDpptRequest = new LoginDpptRequest();
        loginDpptRequest.nsrsbh = sysOrg.TaxId;
        loginDpptRequest.username = sysUser.DPUserName;
        loginDpptRequest.password = sysUser.DPPassword;
        loginDpptRequest.sms = input.Captcha;
        var loginResult = InvoiceHelper.LoginDppt(loginDpptRequest);
        return new PullOutPut() { code = loginResult.code, message = loginResult.message };
    }
    /// <summary>
    /// 下载发票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Download")]
    public async Task<string> Download(InvoiceDownloadRequest input)
    {
        var sysOrg = await App.GetRequiredService<SysOrgService>().GetSysOrgDetail(_userManager.TenantId);
        InvoiceHelper._sysOrg = sysOrg;
        var sysUser = await App.GetRequiredService<SysUserService>().GetBaseInfo();
        InvoiceHelper._sysUser = sysUser;
        var sysTenant = await App.GetRequiredService<SysTenantService>().GetTenantDetail(_userManager.TenantId);
        InvoiceHelper._sysTenant = sysTenant;
        InvoiceHelper._sysTenant.PublicKey = "5dd81a0dee422dc";
        InvoiceHelper._sysTenant.PrivateKey = "7510A4069B2D1C8229C10AFB6";
        if (string.IsNullOrEmpty(InvoiceHelper._sysOrg.TaxId) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPUserName) ||
            string.IsNullOrEmpty(InvoiceHelper._sysUser.DPPassword) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PublicKey) ||
            string.IsNullOrEmpty(InvoiceHelper._sysTenant.PrivateKey)
            )
            return "需维护";
        //if (!string.IsNullOrEmpty(input.Captcha))
        //{
        //    LoginDpptRequest loginDpptRequest = new LoginDpptRequest();
        //    loginDpptRequest.nsrsbh = sysOrg.TaxId;
        //    loginDpptRequest.username = sysUser.DPUserName;
        //    loginDpptRequest.password = sysUser.DPPassword;
        //    loginDpptRequest.sms = input.Captcha;
        //    var loginResult = InvoiceHelper.LoginDppt(loginDpptRequest);
        //    if (!string.IsNullOrEmpty(loginResult))
        //        return "验证码无效";
        //}
        if (input.listFPXX == null || input.listFPXX.Count == 0)
        {
            return "请选择发票";
        }
        int successCount = 0, errorCount = 0;
        foreach (var currentFPXX in input.listFPXX)
        {
            currentFPXX.nsrsbh = InvoiceHelper._sysOrg.TaxId;
            currentFPXX.downflag = "4";
            currentFPXX.username = InvoiceHelper._sysUser.DPUserName ?? "";

            var result = InvoiceHelper.InvoiceDownload(currentFPXX);
            if (result.code == 200)
            {
                successCount++;
                //把发票下载更新到发票表
                var entity = await _rep.AsQueryable().FirstAsync(u => u.InvoiceNum == currentFPXX.fphm);
                if (entity.Id > 0)
                {
                    entity.InvoiceAddress = result.data.pdfUrl;
                    await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
                }
            }
            else
            {
                errorCount++;
                //失败原因更新（）
            }
        }

        return $"成功下载{successCount}条,失败{errorCount}条";
    }

    /// <summary>
    /// 跳转开票
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "JumpToInvoice")]
    public async Task<string> JumpToInvoice(JumpInvoiceInput jumpInvoiceInput)
    {
        if (jumpInvoiceInput.listIds == null || jumpInvoiceInput.listIds.Count == 0) throw new Exception("Error:请选择收款单");
        if (jumpInvoiceInput.listIds.Count > 1) throw new Exception("Error:只能选择一个收款单");

        string zyczydm = _userManager.zyczydm ?? "", zyid = _userManager.zyid ?? "", nsrsbh = _userManager.nsrsbh ?? "", token = "";
        if (string.IsNullOrEmpty(zyid))
        {
            var sysOrg = await App.GetRequiredService<SysOrgService>().GetSysOrgDetail(_userManager.TenantId);
            if (sysOrg != null)
            {
                zyczydm = sysOrg.TaxId;
                nsrsbh = sysOrg.TaxId;
            }

            token = InvoiceHelper.GetTokenJump(zyczydm);
            if (token.Contains("code:") && token.Contains("message:"))
                return "Error:" + token.Split("message:")[1];

            zyid = InvoiceHelper.QueryZyidByNsrsbh(sysOrg.TaxId, token);
            if (zyczydm.IsNullOrEmpty()) zyczydm = sysOrg.TaxId;
            if (nsrsbh.IsNullOrEmpty()) nsrsbh = sysOrg.TaxId;

            var user = await _sysUserRep.AsQueryable().Includes(t => t.SysOrg).Filter(null, true).FirstAsync(u => u.Account.Equals(_userManager.Account));
            _ = user ?? throw Oops.Oh(ErrorCodeEnum.D0009);
            if (!zyczydm.IsNullOrEmpty() && !zyid.IsNullOrEmpty() && !nsrsbh.IsNullOrEmpty())
            {
                user.zyczydm = zyczydm;
                user.zyid = zyid;
                user.nsrsbh = nsrsbh;
                await _sysUserRep.UpdateAsync(user);
            }
        }


        if (string.IsNullOrEmpty(zyczydm) || string.IsNullOrEmpty(zyid) || string.IsNullOrEmpty(nsrsbh))
            throw new Exception("Error:纳税人识别号信息不完整，请重新登录后再试");

        var listIds = jumpInvoiceInput.listIds;
        var listReceipts = await _repReceipt.GetListAsync(x => listIds.Contains(x.Id));
        if (listReceipts == null || listReceipts.Count == 0)
            throw new Exception("Error:收款单不存在，请刷新后重试");

        if (listReceipts.Exists(x => x.PaymentStatus.ToInt(0) < 2))
            throw new Exception("收款单状态不正确，请刷新后重试");

        var listSalescontracts = await _repSalescontract.GetListAsync(x => listReceipts.Select(r => r.SuperiorOrder).Contains(x.SalesOrder));
        if (listSalescontracts == null || listSalescontracts.Count == 0) throw new Exception("Error:销售合约不存在，请刷新后重试");

        var listCustoms = await _pubCustom.AsQueryable()
                   .Where((u) => listSalescontracts.Select(x => x.CustomerId).Contains(u.Id))
                                               .Where((u) => u.IsDelete == false)
                                               .ToListAsync();

        if (listCustoms == null || listCustoms.Count == 0) throw new Exception("Error:客户不存在，请刷新后重试");
        //if (listCustoms.Count > 1) throw new Exception("Error:不能同时给2个客户开发票");

        var listSaleOfGoods = await _saleOfGoods.AsQueryable()
                                            .Where((u) => listSalescontracts.Select(x => x.Id).Contains(u.SalesOrder))
                                            .Where((u) => u.IsDelete == false)
                                             .Select(u => new SaleOfGoodsOutput
                                             {
                                                 brandName = u.Warehousegoods.Brand,
                                                 productCode = u.Warehousegoods.Code,
                                                 specsName = u.Warehousegoods.Specs,
                                                 tradeName = u.Warehousegoods.Name,
                                                 unitName = u.Warehousegoods.WarehouseGoodsUnit.Name,
                                                 puchAmt = u.puchAmt,
                                                 puchPrice = u.puchPrice,
                                                 puchQty = u.puchQty,
                                                 goodsId = u.goodsId,
                                                 unit = u.Warehousegoods.Unit
                                             }).ToListAsync();

        if (listSaleOfGoods == null || listSaleOfGoods.Count == 0)
            throw new Exception("Error:商品信息不存在，请刷新后重试");

        var cir = new CreateInvoiceRequest();

        var generator = new SnowflakeGenerator();
        long documentNum = generator.NextId();
        cir.DocumentNumber = documentNum.ToString();
        //cir.DocumentNumber = listReceipts.FirstOrDefault().Id.ToString();

        cir.TaxpayerNumber = nsrsbh;
        cir.InvoiceTypeCode = jumpInvoiceInput.invoiceType;
        cir.BuyerTaxNumber = listCustoms[0].TaxId;
        cir.BuyerName = listCustoms[0].Name;
        cir.BuyerAddressPhone = listCustoms[0].Address + " " + listCustoms[0].Phone;
        cir.BuyerBankAccount = listCustoms[0].BankCode;

        cir.InvoiceGoodsList = new List<JumpInvoiceGoods> { };

        foreach (var item in listSaleOfGoods)
        {
            var goodsInfo = new JumpInvoiceGoods
            {
                ProductName = item.tradeName,
                ProductCode = item.productCode,
                Unit = item.unitName,
                Specification = item.specsName,
                UnitPrice = item.puchPrice.ToString(),
                Quantity = item.puchQty.ToString(),
                Amount = item.puchAmt.ToString(),
                TaxRate = "",
                TaxAmount = ""
            };
            cir.InvoiceGoodsList.Add(goodsInfo);
        }

        if (token.IsNullOrEmpty())
        {
            token = InvoiceHelper.GetTokenJump(zyczydm);
            if (token.Contains("code:") && token.Contains("message:"))
                return "Error:" + token.Split("message:")[1];
        }

        var rs = InvoiceHelper.CreateGoodsInfo(cir, token, zyid);
        if (rs.Contains("code:") && rs.Contains("message:"))
            return "Error:" + rs.Split("message:")[1];

        InvoicingRecord invoicingRecord = new InvoicingRecord();
        invoicingRecord.DocumentNumber = cir.DocumentNumber;
        invoicingRecord.ReceiptID = listReceipts.FirstOrDefault().Id;
        invoicingRecord.Status = 0;
        await _repInvoRecord.InsertAsync(invoicingRecord);

        //https://ep.b.newtimeai.com/#/?token=&urlFlag=&cgid=&cgnsrsbh=
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append("https://ep.b.newtimeai.com/#/?token=");
        stringBuilder.Append(token);
        stringBuilder.Append("&urlFlag=Invoice/");
        switch (jumpInvoiceInput.invoiceType)
        {
            case "82":
                stringBuilder.Append("fullpowerPtfp");
                break;
            case "81":
                stringBuilder.Append("fullpowerZyfp");
                break;
            case "86":
                stringBuilder.Append("fullpowerPtzp");
                break;
            case "85":
                stringBuilder.Append("fullpowerZyzp");
                break;
        }
        stringBuilder.Append("&cgid=");
        stringBuilder.Append(cir.DocumentNumber);
        stringBuilder.Append("&cgnsrsbh=");
        stringBuilder.Append(cir.TaxpayerNumber);

        return stringBuilder.ToString();
    }
}

