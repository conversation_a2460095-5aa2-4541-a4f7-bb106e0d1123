﻿using SqlSugar;

namespace Admin.NET.Application;

/// <summary>
/// 商品采购明细输出参数
/// </summary>
public class WarehousePurchaseMXOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long PurchaseId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public string WarehousegoodsName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 采购数量
    /// </summary>
    public int? PuchQty { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? PuchPrice { get; set; }

    /// <summary>
    /// 采购金额
    /// </summary>
    public decimal? PuchAmt { get; set; }

    /// <summary>
    /// 入库数量
    /// </summary>
    public int? RcvQty { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public string PubSupplierName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public  bool IsDelete { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string BrandName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string SpecsName { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string BarcodeName { get; set; }
}


