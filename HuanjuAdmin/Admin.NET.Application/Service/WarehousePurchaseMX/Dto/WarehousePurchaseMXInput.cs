﻿using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品采购明细基础输入参数
/// </summary>
public class WarehousePurchaseMXBaseInput
{

    /// <summary>
    /// 商品ID
    /// </summary>
    public virtual long GoodsId { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 采购数量
    /// </summary>
    public virtual int? PuchQty { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    public virtual decimal? PuchPrice { get; set; }

    /// <summary>
    /// 采购金额
    /// </summary>
    public virtual decimal? PuchAmt { get; set; }

    /// <summary>
    /// 入库数量
    /// </summary>
    public virtual int? RcvQty { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    [SugarColumn(ColumnDescription = "软删除")]
    public virtual bool IsDelete { get; set; } = false;

}

/// <summary>
/// 商品采购明细分页查询输入参数
/// </summary>
public class WarehousePurchaseMXInput : BasePageInput
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

}

/// <summary>
/// 商品采购明细增加输入参数
/// </summary>
public class AddWarehousePurchaseMXInput : WarehousePurchaseMXBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 采购单ID
    /// </summary>
    public long? PurchaseId { get; set; }
}

/// <summary>
/// 商品采购明细删除输入参数
/// </summary>
public class DeleteWarehousePurchaseMXInput : BaseIdInput
{
    /// <summary>
    /// 采购单ID
    /// </summary>
    public long PurchaseId { get; set; }
}

/// <summary>
/// 商品采购明细更新输入参数
/// </summary>
public class UpdateWarehousePurchaseMXInput : WarehousePurchaseMXBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 采购单ID
    /// </summary>
    public long PurchaseId { get; set; }

}

/// <summary>
/// 商品采购明细主键查询输入参数
/// </summary>
public class QueryByIdWarehousePurchaseMXInput : DeleteWarehousePurchaseMXInput
{

}
