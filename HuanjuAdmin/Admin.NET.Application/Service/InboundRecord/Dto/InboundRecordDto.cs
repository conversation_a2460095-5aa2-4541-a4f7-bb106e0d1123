﻿namespace Admin.NET.Application;

    /// <summary>
    /// 入库记录输出参数
    /// </summary>
    public class InboundRecordDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 入库明细Id
        /// </summary>
        public long? WarehouseIncordMxId { get; set; }
        
        /// <summary>
        /// 入库数量
        /// </summary>
        public int? InBoundCount { get; set; }
        
        /// <summary>
        /// 打印次数
        /// </summary>
        public int? PrintCount { get; set; }
        
        /// <summary>
        /// 批次Id
        /// </summary>
        public long? WarehouseBatchId { get; set; }
        
    }
