﻿namespace Admin.NET.Application;

    /// <summary>
    /// 入库记录输出参数
    /// </summary>
    public class InboundRecordOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }

       /// <summary>
       /// 入库明细Id
       /// </summary>
       public long? WarehouseIncordMxId { get; set; }

       /// <summary>
       /// 入库数量
       /// </summary>
       public int? InBoundCount { get; set; }

       /// <summary>
       /// 打印次数
       /// </summary>
       public int? PrintCount { get; set; }

       /// <summary>
       /// 批次Id
       /// </summary>
       public long? WarehouseBatchId { get; set; }

       /// <summary>
       /// 是否良品
       /// </summary>
       public bool? GoodProduct { get; set; }

       /// <summary>
       /// 产品类型描述（良品/次品）
       /// </summary>
       public string ProductTypeDesc => GoodProduct == true ? "良品" : GoodProduct == false ? "次品" : "未知";

    }


