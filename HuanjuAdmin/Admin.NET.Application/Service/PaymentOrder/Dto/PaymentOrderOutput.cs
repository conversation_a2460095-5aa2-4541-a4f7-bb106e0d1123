﻿using Admin.NET.Application.Enum;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 付款单输出参数
/// </summary>
public class PaymentOrderOutput
{
    public long Id { get; set; }
    /// <summary>
    /// 付款单号
    /// </summary>
    public string? PaymentNo { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNum { get; set; }

    ///// <summary>
    ///// 销售单号
    ///// </summary>
    //public string? SalesOrder { get; set; }

    /// <summary>
    /// 单据金额
    /// </summary>
    public decimal? DocumentAmount { get; set; }

    /// <summary>
    /// 已付金额
    /// </summary>
    public decimal? AmountPaid { get; set; }

    /// <summary>
    /// 已收票金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }

    /// <summary>
    /// 成本类型
    /// </summary>
    public int? CostType { get; set; }

    /// <summary>
    /// 支出名目
    /// </summary>
    public string? ExpenditureCategory { get; set; }
    /// <summary>
    /// 二级科目
    /// </summary>
    public string? SecondaryAccount { get; set; }
    /// <summary>
    /// 付款状态
    /// </summary>
    public int? PaymentStatus { get; set; }

    /// <summary>
    /// 交易账户
    /// </summary>
    public long? Trading { get; set; }

    /// <summary>
    /// 交易账户
    /// </summary>
    public string TradingName { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public int? InvoiceStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long CreateUserId { get; set; }
    /// <summary>
    /// 创建者
    /// </summary>
    public string CreateUserName { get; set; }
    /// <summary>
    /// 采购单号
    /// </summary>
    public string PurchaseNumber { get; set; }
    /// <summary>
    /// 摘要
    /// </summary>
    public string? Abstract { get; set; }
    /// <summary>
    /// 发票单号
    /// </summary>
    public string InvoiceNo { get; set; }
}


