﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using Admin.NET.Core.Util.Npoi;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Magicodes.ExporterAndImporter.Core.Extension;
using Nest;
using SqlSugar;
using System;
using System.ComponentModel.Design;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 付款单服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class PaymentOrderService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<warerevenue> _repWarerevenue;
    private readonly SqlSugarRepository<Paymentorder> _rep;
    private readonly SqlSugarRepository<InvoiceManage> _repInvoice;
    public PaymentOrderService(SqlSugarRepository<Paymentorder> rep, UserManager userManager, SqlSugarRepository<warerevenue> repWarerevenue, SqlSugarRepository<InvoiceManage> repInvoice)
    {
        _userManager = userManager;
        _repWarerevenue = repWarerevenue;
        _rep = rep;
        _repInvoice = repInvoice;
    }

    /// <summary>
    /// 分页查询付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<PaymentOrderOutput>> Page(PaymentOrderInput input)
    {
        var checkStatus = !input.PaymentStatus.IsNullOrWhiteSpace() ? input.PaymentStatus.Split(',') : null;
        var query = _rep.AsQueryable()
                    .LeftJoin<SysUser>((u, user) => u.CreateUserId == user.Id)
                    .LeftJoin<TradingAccounts>((u, user, t) => u.Trading == t.Id)
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.UnitName), u => u.supplier.Name.Contains(input.UnitName.Trim()))
                    .WhereIF(input.InvoiceStatus != null, u => u.InvoiceStatus == input.InvoiceStatus)
                    .WhereIF(input.PaymentStatus != null, u => checkStatus.Contains(u.PaymentStatus.ToString(), true))
                    .Select((u, user, t) => new PaymentOrderOutput
                    {
                        Id = u.Id,
                        SupplierId = u.SupplierId,
                        UnitName = u.supplier.Name,
                        PaymentNo = u.PaymentNo,
                        ContractNum = u.ContractNum,
                        DocumentAmount = u.DocumentAmount,
                        ReceivedAmount = u.ReceivedAmount,
                        CostType = u.CostType,
                        ExpenditureCategory = u.ExpenditureCategory,
                        SecondaryAccount = u.SecondaryAccount,
                        PaymentStatus = u.PaymentStatus,
                        Trading = u.Trading,
                        TradingName = t.Name,
                        InvoiceStatus = u.InvoiceStatus,
                        Notes = u.Notes,
                        Abstract = u.Abstract,
                        CreateTime = u.CreateTime,
                        CreateUserName = user.RealName,
                        PurchaseNumber = u.PurchaseNumber,
                        AmountPaid = u.AmountPaid,
                        InvoiceNo = u.InvoiceNo
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddPaymentOrderInput input)
    {
        var entity = input.Adapt<Paymentorder>();
        entity.PaymentNo = await App.GetRequiredService<PubOrderService>().GetNewOrder("FK");
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletePaymentOrderInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatePaymentOrderInput input)
    {
        var entity = await _rep.AsQueryable()
            .Includes(x => x.supplier)
            .FirstAsync(u => u.Id == input.Id);

        entity.Abstract = input.Abstract;
        entity.Trading = input.Trading;
        entity.DocumentAmount = input.DocumentAmount;
        entity.ExpenditureCategory = input.ExpenditureCategory;
        entity.SecondaryAccount = input.SecondaryAccount;
        entity.InvoiceStatus = input.InvoiceStatus;
        entity.Notes = input.Notes;

        if (!string.IsNullOrEmpty(input.InvoiceNo))
        {
            var invoiceNo = await _repInvoice.GetFirstAsync(u => u.InvoiceNum == input.InvoiceNo) ?? throw Oops.Oh($"发票号码{input.InvoiceNo}不存在，请确认！");
            entity.InvoiceNo = input.InvoiceNo;
            entity.ReceivedAmount = input.ReceivedAmount;
            entity.InvoiceStatus = 2;
        }
        if (entity.SupplierId != input.SupplierId)
        {
            entity.SupplierId = input.SupplierId;
            var supplier = await App.GetService<PubSupplierService>().Get(new QueryByIdPubSupplierInput { Id = input.SupplierId ?? 0 });
            entity.supplier = supplier;
        }

        decimal originalAmountPaid = 0;
        if (entity != null)
        {
            originalAmountPaid = Convert.ToDecimal(entity.AmountPaid);
        }
        entity.AmountPaid = originalAmountPaid + input.AmountPaid;
        if (input.AmountPaid > 0 && input.AmountPaid < Convert.ToDecimal(entity.DocumentAmount))
        {
            entity.PaymentStatus = Enum.PaymentStatusEnum.Partial.ToInt();
        }
        else if (input.AmountPaid >= Convert.ToDecimal(entity.DocumentAmount))
        {
            entity.PaymentStatus = Enum.PaymentStatusEnum.Received.ToInt();
        }

        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        //插入收支明细表
        warerevenue warerevenue = new warerevenue
        {
            revenueTime = DateTime.Now,
            CompanyID = entity.SupplierId,
            contactunits = entity.supplier.Name,
            ordernumber = entity.PaymentNo,
            subject = entity.ExpenditureCategory,
            levelsubject = entity.SecondaryAccount,
            expenditureamount = entity.AmountPaid,
            handledby = entity.UpdateUserId,
            TenantId = entity.TenantId,
            revenueType = 2
        };
        await _repWarerevenue.InsertAsync(warerevenue);
    }

    /// <summary>
    /// 提交付款单
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Submit")]
    public async Task<bool> Submit(List<long> listIds)
    {
        foreach (var id in listIds)
        {
            var entity = _rep.GetById(id);
            if (entity != null && entity.PaymentStatus == 0)
            {
                entity.PaymentStatus = 1;
                await _rep.UpdateAsync(entity);
            }
            else
            {
                throw Oops.Oh(ErrorCodeEnum.D1002);
            }
        }
        return true;
    }

    /// <summary>
    /// 撤回付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Retract")]
    public async Task<bool> Retract(List<long> listIds)
    {
        foreach (var id in listIds)
        {
            var entity = _rep.GetById(id);
            if (entity != null && entity.PaymentStatus == 1)
            {
                entity.PaymentStatus = 0;
                await _rep.UpdateAsync(entity);
            }
            else
            {
                throw Oops.Oh(ErrorCodeEnum.D1002);
            }
        }
        return true;
    }

    /// <summary>
    /// 获取付款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Paymentorder> Get([FromQuery] QueryByIdPaymentOrderInput input)
    //{
    //}

    /// <summary>
    /// 获取付款单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<PaymentOrderOutput>> List([FromQuery] PaymentOrderInput input)
    {
        return await _rep.AsQueryable().Select<PaymentOrderOutput>().ToListAsync();
    }


    /// <summary>
    /// 导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Import")]
    public async Task<FileContentResult> Import(PaymentOrderInput input)
    {
        var query = _rep.AsQueryable().LeftJoin<TradingAccounts>((x, t) => x.Trading == t.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.UnitName), u => u.supplier.Name.Contains(input.UnitName.Trim()))
                    .WhereIF(input.InvoiceStatus > 0, u => u.InvoiceStatus == input.InvoiceStatus)
            .Select((x, t) => new PaymentOrderOutput
            {
                PaymentNo = x.PaymentNo,
                UnitName = x.supplier.Name,
                ContractNum = x.ContractNum,
                AmountPaid = x.AmountPaid,
                //InvoicedAmount=x.ReceivedAmount,
                //CostType = (Enum.CostTypeEnum)x.CostType,
                //IncomeCategory= (Enum.PayEnum)x.ExpenditureCategory, 
                //PaymentStatus= (Enum.PaymentStatusEnum)x.PaymentStatus,
                TradingName = t.Name,
                InvoiceStatus = x.InvoiceStatus,
                Notes = x.Notes
            });
        query = query.OrderBuilder(input);
        Dictionary<string, string> dicColumns = new Dictionary<string, string>
        {
            ["ReceiptNo"] = "付款单号",
            ["UnitName"] = "单位名称",
            ["ContractNum"] = "合同编号",
            ["AmountPaid"] = "已付金额",
            ["InvoicedAmount"] = "已开票金额",
            ["IncomeType"] = "成本类型",
            ["IncomeCategory"] = "支出名目",
            ["PaymentStatus"] = "付款状态",
            ["TradingName"] = "交易账户",
            ["InvoiceStatus"] = "发票状态",
            ["Notes"] = "备注",
        };
        List<WarehouseStoreOutput> lw = new List<WarehouseStoreOutput>();
        var res = await query.ToPagedListAsync(input.Page, input.PageSize);
        var export = NpoiHelper.ExportExcel(res.Items.ToList(), dicColumns);
        var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        return new FileContentResult(export, mimeType)
        {
            // FileDownloadName = fileName
        };
    }

    /// <summary>
    /// 根据采购单增加付款单
    /// </summary>
    [HttpPost]
    public async Task AddByListPurchase(WarehousePurchase purchase)
    {
        if (purchase != null)
        {
            Paymentorder paymentorder = new Paymentorder();

            var orderNumber = await App.GetService<PubOrderService>().GetNewOrder("FK");
            if (orderNumber.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }

            paymentorder.PaymentNo = orderNumber;
            paymentorder.SupplierId = purchase.SupplierId;
            paymentorder.ContractNum = orderNumber;
            paymentorder.DocumentAmount = purchase.ActualAmt;
            paymentorder.PaymentStatus = 0;
            paymentorder.Trading = 0;
            paymentorder.PurchaseNumber = purchase.OrderNumber;
            paymentorder.InvoiceStatus = 0;

            await _rep.AsInsertable(paymentorder).ExecuteCommandAsync();
        }
    }
    // <summary>
    /// 中止
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Suspend")]
    public async Task Suspend(List<long> listInputs)
    {
        var rpList = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();

        foreach (var rp in rpList)
        {
            if (rp.PaymentStatus != 2)
            {
                throw Oops.Oh("状态为部分付款才能执行中止操作");
            }

            //var listStore = _repSale.AsQueryable()
            //    .InnerJoin<WarehouseInrecordMX>((u, mx) => u.WarehouseId == rp.Warehouseid && u.TradeID == mx.GoodsId && mx.InrecordId == rp.Id && mx.DocumentNum != mx.RcvQty)
            //    .Select((u, mx) => new WarehouseStore
            //    {
            //        Id = u.Id,
            //        IntransitNum = u.IntransitNum - (mx.DocumentNum - mx.RcvQty),

            //    }).ToListAsync().Result;

            //if (listStore != null && listStore.Count > 0)
            //{
            //    await _repSale.AsUpdateable(listStore).UpdateColumns(x => new { x.IntransitNum }).ExecuteCommandAsync();
            //}
            rp.PaymentStatus = 4;
            await _rep.UpdateAsync(rp);
        }
    }
    /// 付款单收票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "InvoiceRecieve")]
    public async Task InvoiceRecieve(UpdatePaymentOrderInput input)
    {
        var entity = await _rep.AsQueryable()
            .Includes(x => x.supplier)
            .FirstAsync(u => u.Id == input.Id);

        if (!string.IsNullOrEmpty(input.InvoiceNo))
        {
            var invoiceNo = await _repInvoice.GetFirstAsync(u => u.InvoiceNum == input.InvoiceNo) ?? throw Oops.Oh($"发票号码{input.InvoiceNo}不存在，请确认！");
            entity.InvoiceNo = input.InvoiceNo;
            entity.ReceivedAmount = input.ReceivedAmount;
            entity.InvoiceStatus = 2;
        }
        string[] columns = new string[] { "InvoiceNo", "ReceivedAmount", "InvoiceStatus" };
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).UpdateColumns(columns).ExecuteCommandAsync();
    }
}

