﻿using Microsoft.AspNetCore.Identity;
using SqlSugar;

namespace Admin.NET.Application;

/// <summary>
/// 商品明细输出参数
/// </summary>
public class SaleOfGoodsOutput
{
    public long Id { get; set; }
    /// <summary>
    /// 商品名称
    /// </summary>
    public string? tradeName { get; set; }

    public string productCode { get; set; }

    public string brandName { get; set; }

    public string specsName { get; set; }

    public string unitName { get; set; }

    public decimal? puchPrice { get; set; }

    public int? puchQty { get; set; }


    public decimal? puchAmt { get; set; }
    public long goodsId { get; set; }
    /// <summary>
    /// 单位
    /// </summary>
    public long? unit { get; set; }
}


