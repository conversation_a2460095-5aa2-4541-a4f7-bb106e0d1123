﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using Nest;
using SqlSugar;
using System.Collections.Generic;

namespace Admin.NET.Application;
/// <summary>
/// 商品明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SaleOfGoodsService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<SaleOfGoods> _rep;
    public SaleOfGoodsService(SqlSugarRepository<SaleOfGoods> rep, ISqlSugarClient db)
    {
        _db = db;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询商品明细
    /// </summary> q
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SaleOfGoodsOutput>> Page(SaleOfGoodsInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.TradeName.Contains(input.TradeName.Trim()))
                    .WhereIF(input.puchQty > 0, u => u.puchQty == input.puchQty)
                    .Where(u => u.SalesOrder == input.SalesOrder)

                    .Select(u => new SaleOfGoodsOutput
                    {
                        Id = u.Id,
                        brandName = u.Warehousegoods.Brand,
                        productCode = u.Warehousegoods.Code,
                        specsName = u.Warehousegoods.Specs,
                        tradeName = u.Warehousegoods.Name,
                        unitName = u.Warehousegoods.WarehouseGoodsUnit.Name,
                        puchAmt = u.puchAmt,
                        puchPrice = u.puchPrice,
                        puchQty = u.puchQty,
                        goodsId = u.goodsId,
                        unit = u.Warehousegoods.Unit
                    });
        ;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加商品明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSaleOfGoodsInput input)
    {
        //var query = _reps.AsQueryable().Where(x=>x.TradeName==input.TradeName).Select(x=>x.is)
        var entity = input.Adapt<SaleOfGoods>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSaleOfGoodsInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateSaleOfGoodsInput input)
    {
        var entity = input.Adapt<SaleOfGoods>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取商品明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<SaleOfGoods> Get([FromQuery] QueryByIdSaleOfGoodsInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品明细列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SaleOfGoodsOutput>> List([FromQuery] SaleOfGoodsInput input)
    {
        return await _rep.AsQueryable().Select<SaleOfGoodsOutput>().ToListAsync();
    }


    public async Task AddOrUpdate(List<SaleOfGoods> listMx)
    {
        // 使用 System.Linq 的 Where 方法
        var needInsert = listMx.FindAll(item => item.Id == 0);
        var needUpdate = listMx.FindAll(item => item.Id > 0);

        // 执行插入
        if (needInsert.Count > 0)
        {
            await _rep.InsertRangeAsync(needInsert);
        }

        // 逐条执行更新，避免使用复杂的JOIN语法
        foreach (var item in needUpdate)
        {
            // 检查记录是否存在
            bool exists = await _rep.IsAnyAsync(x => x.Id == item.Id);

            if (exists)
            {
                await _rep.AsUpdateable(item)
                    .IgnoreColumns(ignoreAllNullColumns: true)
                    .ExecuteCommandAsync();
            }
            else
            {
                // 如果记录不存在，执行插入
                await _rep.InsertAsync(item);
            }
        }
    }

}

