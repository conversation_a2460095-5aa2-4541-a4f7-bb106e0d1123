﻿namespace Admin.NET.Application;

    /// <summary>
    /// 应收应付输出参数
    /// </summary>
    public class WareArapDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 待收金额
        /// </summary>
        public decimal? PendingAmount { get; set; }
        
        /// <summary>
        /// 代付金额
        /// </summary>
        public decimal? AmountPaid { get; set; }
        
        /// <summary>
        /// 来往单位
        /// </summary>
        public long Contactunits { get; set; }
        
        /// <summary>
        /// 类别
        /// </summary>
        public int category { get; set; }
        
    }
