﻿namespace Admin.NET.Application;

/// <summary>
/// 应收应付输出参数
/// </summary>
public class WareArapOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    //public long Id { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int? Number { get; set; }

    /// <summary>
    /// 应收金额
    /// </summary>
    public decimal? PendingAmount { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public decimal? AmountPaid { get; set; }
    /// <summary>
    /// 待收金额
    /// </summary>
    public decimal? OutstandingAmount { get; set; }
    /// <summary>
    /// 待收已中止金额
    /// </summary>
    public decimal? StopAmountReceivable { get; set; }

    /// <summary>
    /// 待付已中止金额
    /// </summary>
    public decimal? StopAmountPayable { get; set; }
    /// <summary>
    /// 应付金额
    /// </summary>
    public decimal? PendingAmountPayment { get; set; }

    /// <summary>
    /// 已付金额
    /// </summary>
    public decimal? AmountPaidPayment { get; set; }

    /// <summary>
    /// 待付金额
    /// </summary>
    public decimal? AmountUnpaidPayment { get; set; }
    /// <summary>
    /// 来往单位
    /// </summary>
    public string Contactunits { get; set; }

    /// <summary>
    /// 类别
    /// </summary>
    public int Category { get; set; }
    /// <summary>
    /// 类别
    /// </summary>
    public string CategoryName { get; set; }
}


