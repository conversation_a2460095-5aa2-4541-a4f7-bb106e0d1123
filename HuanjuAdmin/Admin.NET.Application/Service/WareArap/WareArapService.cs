﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Util.Npoi;
using SqlSugar;
using System;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 应收应付服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WareArapService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WareArap> _rep;
    private readonly SqlSugarRepository<Receipt> _repReceipt;
    private readonly SqlSugarRepository<Paymentorder> _repPaymentOrder;
    private readonly UserManager _userManager;
    public WareArapService(
        UserManager userManager,
        SqlSugarRepository<Receipt> repReceipt,
        SqlSugarRepository<Paymentorder> repPaymentOrder,
        SqlSugarRepository<WareArap> rep)
    {
        _userManager = userManager;
        _rep = rep;
        _repReceipt = repReceipt;
        _repPaymentOrder = repPaymentOrder;
    }

    /// <summary>
    /// 分页查询应收应付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WareArapOutput>> Page(WareArapInput input)
    {
        List<WareArapOutput> list = new List<WareArapOutput>();
        if (input.Category == 0 || input.Category == null)
        {
            var accept = _repReceipt.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Contactunits), u => u.UnitName.Contains(input.Contactunits))
            .Where(u => u.PaymentStatus > 0)
            .WhereIF(input.StartDate != null, u => u.CreateTime >= input.StartDate)
            .WhereIF(input.EndDate != null, u => u.CreateTime < input.EndDate.ToDateTime().AddDays(1))
            .Select<Receipt>().ToList();
            if (accept.Count > 0)
            {
                //循环每个单位，进行计算
                accept.Select(u => u.UnitName).Distinct().ToList().ForEach(u =>
                {
                    decimal PendingAmount = 0m;
                    decimal AmountPaid = 0m;
                    decimal StopAmount = 0m;
                    accept.Where(m => m.UnitName == u).ToList().ForEach(n =>
                     {
                         PendingAmount += n.DocumentAmount ?? 0;
                         AmountPaid += n.AmountReceived ?? 0;
                         if (n.PaymentStatus == 4) StopAmount += ((n.DocumentAmount ?? 0) - (n.AmountReceived ?? 0));
                     });
                    list.Add(new WareArapOutput { Contactunits = u, Category = 0, CategoryName = "应收", PendingAmount = PendingAmount, AmountPaid = AmountPaid, OutstandingAmount = PendingAmount - AmountPaid - StopAmount, StopAmountReceivable = StopAmount });
                });
            }
        }
        if (input.Category == 1 || input.Category == null)
        {
            var payment = _repPaymentOrder.AsQueryable()
                    .LeftJoin<PubSupplier>((u, s) => u.SupplierId == s.Id && s.TenantId == u.TenantId)  // 添加租户ID条件
                    .Where((u, s) => u.TenantId == _userManager.TenantId)
                    .Where((u, s) => u.SupplierId != null)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Contactunits), (u, s) => s.Name.Contains(input.Contactunits))
                    .Where((u, s) => u.PaymentStatus > 0)
                    .WhereIF(input.StartDate != null, u => u.CreateTime >= input.StartDate)
                    .WhereIF(input.EndDate != null, u => u.CreateTime < input.EndDate.ToDateTime().AddDays(1))
                    .Select((u, s) => new Paymentorder
                    {
                        Id = u.Id,
                        SupplierId = u.SupplierId,
                        PaymentNo = u.PaymentNo,
                        ContractNum = u.ContractNum,
                        DocumentAmount = u.DocumentAmount,
                        AmountPaid = u.AmountPaid,
                        ReceivedAmount = u.ReceivedAmount,
                        CostType = u.CostType,
                        ExpenditureCategory = u.ExpenditureCategory,
                        SecondaryAccount = u.SecondaryAccount,
                        PaymentStatus = u.PaymentStatus,
                        Trading = u.Trading,
                        InvoiceStatus = u.InvoiceStatus,
                        Notes = u.Notes,
                        PurchaseNumber = u.PurchaseNumber,
                        Abstract = u.Abstract,
                        supplier = s  // 显式映射供应商信息
                    }).ToList();
            if (payment.Count > 0)
            {
                //循环每个单位，进行计算
                payment.Select(u => u.supplier.Name).Distinct().ToList().ForEach(u =>
                {
                    decimal PendingAmount = 0m;
                    decimal AmountPaid = 0m;
                    decimal StopAmount = 0m;
                    payment.Where(m => m.supplier.Name == u).ToList().ForEach(n =>
                    {
                        PendingAmount += n.DocumentAmount ?? 0;
                        AmountPaid += n.AmountPaid ?? 0;
                        if (n.PaymentStatus == 4) StopAmount += ((n.DocumentAmount ?? 0) - (n.AmountPaid ?? 0));
                    });
                    list.Add(new WareArapOutput { Contactunits = u, Category = 1, CategoryName = "应付", PendingAmountPayment = PendingAmount, AmountPaidPayment = AmountPaid, AmountUnpaidPayment = PendingAmount - AmountPaid - StopAmount, StopAmountPayable = StopAmount });
                });
            }
        }
        return list.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加应收应付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWareArapInput input)
    {
        var entity = input.Adapt<WareArap>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除应收应付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost]
    //[ApiDescriptionSettings(Name = "Delete")]
    //public async Task Delete(DeleteWareArapInput input)
    //{
    //    await _rep.FakeDeleteAsync(entity);   //假删除
    //}

    /// <summary>
    /// 更新应收应付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWareArapInput input)
    {
        var entity = input.Adapt<WareArap>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取应收应付
    /// </summary>
    /// <param name="input"></param>
    ///// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<WareArap> Get([FromQuery] QueryByIdWareArapInput input)
    //{
    //}

    /// <summary>
    /// 获取应收应付列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WareArapOutput>> List([FromQuery] WareArapInput input)
    {
        return await _rep.AsQueryable().Select<WareArapOutput>().ToListAsync();
    }




    /// <summary>
    /// 导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Import")]
    public async Task<FileContentResult> Import(WareArapInput input)
    {
        List<WareArapOutput> list = new List<WareArapOutput>();
        if (input.Category == 0 || input.Category == null)
        {
            var accept = _repReceipt.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Contactunits), u => u.UnitName.Contains(input.Contactunits))
            .Where(u => (u.PaymentStatus == 1 || u.PaymentStatus == 2))
            .Select<Receipt>().ToList();
            if (accept.Count > 0)
            {
                //循环每个单位，进行计算
                accept.Select(u => u.UnitName).Distinct().ToList().ForEach(u =>
                {
                    decimal PendingAmount = 0m;
                    decimal AmountPaid = 0m;
                    accept.Where(m => m.UnitName == u).ToList().ForEach(n =>
                    {
                        PendingAmount += n.DocumentAmount ?? 0;
                        AmountPaid += n.AmountReceived ?? 0;
                    });
                    list.Add(new WareArapOutput { Number = null, Contactunits = u, Category = 0, CategoryName = "应收", PendingAmount = PendingAmount, AmountPaid = AmountPaid, OutstandingAmount = PendingAmount - AmountPaid });
                });
            }
        }
        if (input.Category == 1 || input.Category == null)
        {
            var payment = _repPaymentOrder.AsQueryable()
           .Where(u => u.TenantId == _userManager.TenantId)
           .WhereIF(!string.IsNullOrWhiteSpace(input.Contactunits), u => u.supplier.Name.Contains(input.Contactunits))
           .Where(u => (u.PaymentStatus == 1 || u.PaymentStatus == 2))
           .Select<Paymentorder>().ToList();
            if (payment.Count > 0)
            {
                //循环每个单位，进行计算
                payment.Select(u => u.supplier.Name).Distinct().ToList().ForEach(u =>
                {
                    decimal PendingAmount = 0m;
                    decimal AmountPaid = 0m;
                    payment.Where(m => m.supplier.Name == u).ToList().ForEach(n =>
                    {
                        PendingAmount += n.DocumentAmount ?? 0;
                        AmountPaid += n.AmountPaid ?? 0;
                    });
                    list.Add(new WareArapOutput { Number = null, Contactunits = u, Category = 1, CategoryName = "应付", PendingAmountPayment = PendingAmount, AmountPaidPayment = AmountPaid, AmountUnpaidPayment = PendingAmount - AmountPaid });
                });
            }
        }
        Dictionary<string, string> dicColumns = new Dictionary<string, string>
        {
            ["Number"] = "序号",
            ["Contactunits"] = "来往单位",
            ["CategoryName"] = "类别",
            ["PendingAmount"] = "应收金额",
            ["AmountPaid"] = "已收金额",
            ["OutstandingAmount"] = "未收金额",
            ["PendingAmountPayment"] = "应付金额",
            ["AmountPaidPayment"] = "已付金额",
            ["AmountUnpaidPayment"] = "未付金额"
        };
        List<WarehouseStoreOutput> lw = new List<WarehouseStoreOutput>();
        var res = list;
        var export = NpoiHelper.ExportExcel(res, dicColumns);
        var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        return new FileContentResult(export, mimeType)
        {
            // FileDownloadName = fileName
        };
    }

}

