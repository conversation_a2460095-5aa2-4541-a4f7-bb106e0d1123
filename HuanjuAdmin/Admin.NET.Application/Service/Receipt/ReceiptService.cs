﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using Elasticsearch.Net;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Magicodes.ExporterAndImporter.Core.Extension;
using Nest;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 收款单服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class ReceiptService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Receipt> _rep;
    UserManager _userManager;
    private readonly SqlSugarRepository<SalesperFormanceplan> _rep2;
    private readonly SqlSugarRepository<Salescontract> _rep3;
    private readonly SqlSugarRepository<warerevenue> _repWarerevenue;
    private readonly SqlSugarRepository<InvoiceManage> _repInvoice;
    private readonly SqlSugarRepository<InvoicingRecord> _repInvoiceRecord;
    public ReceiptService(SqlSugarRepository<Receipt> rep, UserManager userManager,
        SqlSugarRepository<SalesperFormanceplan> rep2,
        SqlSugarRepository<Salescontract> rep3,
        SqlSugarRepository<warerevenue> repWarerevenue,
        SqlSugarRepository<InvoiceManage> repInvoice,
        SqlSugarRepository<InvoicingRecord> repInvoiceRecord
        )
    {
        _rep = rep;
        _userManager = userManager;
        _rep2 = rep2;
        _rep3 = rep3;
        _repWarerevenue = repWarerevenue;
        _repInvoice = repInvoice;
        _repInvoiceRecord = repInvoiceRecord;
    }

    /// <summary>
    /// 分页查询收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<ReceiptOutput>> Page(ReceiptInput input)
    {
        var checkStatus = !string.IsNullOrWhiteSpace(input.PaymentStatus) ? input.PaymentStatus.Split(',') : null;
        var checkInvoiceStatus = !string.IsNullOrWhiteSpace(input.InvoiceStatus) ? input.InvoiceStatus.Split(',') : null;
        var query = _rep.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId)
            .Where(u => u.IsDelete == false)
            .LeftJoin<SysUser>((u, user) => u.CreateUserId == user.Id)
            .LeftJoin<TradingAccounts>((u, user, t) => u.Trading == t.Id)
            .WhereIF(!string.IsNullOrEmpty(input.CreateUserName), (u, user) => user.RealName.Contains(input.CreateUserName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UnitName), u => u.UnitName.Contains(input.UnitName.Trim()))
            .WhereIF(checkStatus != null, u => checkStatus.Contains(u.PaymentStatus.ToString(), true))
            .WhereIF(checkInvoiceStatus != null, u => checkInvoiceStatus.Contains(u.InvoiceStatus.ToString(), true))
            .Select((u, user, t) => new ReceiptOutput
            {
                Id = u.Id,
                UnitName = u.UnitName,
                AmountReceived = u.AmountReceived,
                DocumentAmount = u.DocumentAmount,
                InvoicedAmount = u.InvoicedAmount,
                ContractNum = u.ContractNum,
                CreateTime = u.CreateTime,
                CreateUserName = user.RealName,
                IncomeCategory = u.IncomeCategory,
                IncomeType = u.IncomeType,
                InvoiceStatus = u.InvoiceStatus,
                Notes = u.Notes,
                Abstract = u.Abstract,
                PaymentStatus = u.PaymentStatus,
                ReceiptNo = u.ReceiptNo,
                SuperiorOrder = u.SuperiorOrder,
                Trading = u.Trading,
                TradingName = t.Name,
                InvoiceNo = u.InvoiceNo
            })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddReceiptInput input)
    {
        var entity = input.Adapt<Receipt>();
        entity.ReceiptNo = await App.GetRequiredService<PubOrderService>().GetNewOrder("SK");
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteReceiptInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateReceiptInput input)
    {
        var result = await _rep.GetFirstAsync(u => u.Id == input.Id);
        decimal originalAmountReceived = 0;

        if (result != null)
        {
            originalAmountReceived = Convert.ToDecimal(result.AmountReceived);
        }
        input.AmountReceived = originalAmountReceived + input.AmountReceived;
        if (input.AmountReceived > 0 && input.AmountReceived < Convert.ToDecimal(result.DocumentAmount))
        {
            input.PaymentStatus = Enum.PaymentStatusEnum.Partial;
        }
        else if (input.AmountReceived >= Convert.ToDecimal(result.DocumentAmount))
        {
            input.PaymentStatus = Enum.PaymentStatusEnum.Received;
        }
        var entity = input.Adapt<Receipt>();
        DateTime dtNow = DateTime.Now;
        entity.CreateTime = dtNow;
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        await UpdateStatus(result.SuperiorOrder, input.PaymentStatus.ToInt());
        //插入收支明细表
        warerevenue warerevenue = new warerevenue
        {
            revenueTime = dtNow,
            contactunits = entity.UnitName,
            ordernumber = entity.ReceiptNo,
            subject = entity.IncomeType,
            levelsubject = entity.IncomeCategory,
            Incomeamount = input.AmountReceived,
            handledby = entity.UpdateUserId,
            TenantId = entity.TenantId,
            revenueType = 1
        };
        await _repWarerevenue.InsertAsync(warerevenue);
    }

    /// <summary>
    /// 提交收款单
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Submit")]
    public async Task<bool> Submit(List<long> listIds)
    {
        foreach (var id in listIds)
        {
            var entity = _rep.GetById(id);
            if (entity != null && entity.PaymentStatus == 0)
            {
                entity.PaymentStatus = 1;
                await _rep.UpdateAsync(entity);
                await UpdateStatus(entity.SuperiorOrder, entity.PaymentStatus ?? 0);
            }
            else
            {
                throw Oops.Oh(ErrorCodeEnum.D1002);
            }
        }
        return true;
    }

    /// <summary>
    /// 撤回收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Retract")]
    public async Task<bool> Retract(List<long> listIds)
    {
        foreach (var id in listIds)
        {
            var entity = _rep.GetById(id);
            if (entity != null && entity.PaymentStatus == 1)
            {
                entity.PaymentStatus = 0;
                await _rep.UpdateAsync(entity);
                await UpdateStatus(entity.SuperiorOrder, entity.PaymentStatus ?? 0);
            }
            else
            {
                throw Oops.Oh(ErrorCodeEnum.D1002);
            }
        }
        return true;
    }

    /// <summary>
    /// 获取收款单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Receipt> Get([FromQuery] QueryByIdReceiptInput input)
    //{
    //}

    /// <summary>
    /// 获取收款单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<ReceiptOutput>> List([FromQuery] ReceiptInput input)
    {
        return await _rep.AsQueryable().Select<ReceiptOutput>().ToListAsync();
    }

    public async Task UpdateStatus(string salesOrder, int plan)
    {
        UpdateSalesperFormanceplanInput input1 = new UpdateSalesperFormanceplanInput();
        Func_UpdateStatus func = new Func_UpdateStatus(_rep2, _rep3);
        input1.SalesOrder = salesOrder;
        if (!string.IsNullOrEmpty(input1.SalesOrder))
        {
            var query = _rep3.AsQueryable().Where(u => u.SalesOrder == input1.SalesOrder).Select<Salescontract>().FirstAsync().Result;
            var entity = await _rep2.AsQueryable().Where(u => u.SalesOrder == query.Id.ToString() && u.Type == Enum.SalesTypeEnmu.SendOut).Select<SalesperFormanceplan>().FirstAsync();
            entity.Plan = plan;
            await _rep2.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            await func.UpdateStatus(input1);
        }
    }

    // <summary>
    /// 中止
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Suspend")]
    public async Task Suspend(List<long> listInputs)
    {
        var rpList = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();

        foreach (var rp in rpList)
        {
            if (rp.PaymentStatus != 2)
            {
                throw Oops.Oh("状态为部分收款才能执行中止操作");
            }

            //var listStore = _repSale.AsQueryable()
            //    .InnerJoin<WarehouseInrecordMX>((u, mx) => u.WarehouseId == rp.Warehouseid && u.TradeID == mx.GoodsId && mx.InrecordId == rp.Id && mx.DocumentNum != mx.RcvQty)
            //    .Select((u, mx) => new WarehouseStore
            //    {
            //        Id = u.Id,
            //        IntransitNum = u.IntransitNum - (mx.DocumentNum - mx.RcvQty),

            //    }).ToListAsync().Result;

            //if (listStore != null && listStore.Count > 0)
            //{
            //    await _repSale.AsUpdateable(listStore).UpdateColumns(x => new { x.IntransitNum }).ExecuteCommandAsync();
            //}
            rp.PaymentStatus = 4;
            await _rep.UpdateAsync(rp);
        }
    }
    /// <summary>
    /// 收款单开票回传
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "InvoiceCallBack")]
    public async Task InvoiceCallBack(ReceiptInvoiceInput input)
    {
        var invocingRecord = _repInvoiceRecord.AsQueryable().Where(x => x.DocumentNumber == input.djh).FirstAsync().Result;
        if (invocingRecord == null)
            throw Oops.Oh("开票单据不存在");
        var result = await _rep.GetFirstAsync(u => u.Id == invocingRecord.ReceiptID);
        result.InvoiceNo = result.InvoiceNo.IsNullOrWhiteSpace() ? input.InvoiceNo : result.InvoiceNo + "," + input.InvoiceNo;
        result.InvoicedAmount = result.InvoicedAmount ?? 0 + input.InvoicedAmount;
        result.InvoiceStatus = 2;

        string[] columns = new string[] { "InvoiceNo", "InvoicedAmount", "InvoiceStatus" };
        await _rep.AsUpdateable(result).IgnoreColumns(ignoreAllNullColumns: true).UpdateColumns(columns).ExecuteCommandAsync();

        invocingRecord.Status = 1;
        await _repInvoiceRecord.UpdateAsync(invocingRecord);
    }
}

