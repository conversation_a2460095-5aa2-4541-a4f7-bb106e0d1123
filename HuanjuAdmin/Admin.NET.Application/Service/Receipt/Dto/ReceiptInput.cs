﻿using Admin.NET.Application.Enum;
using System.ComponentModel.DataAnnotations;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ShopCouponGetResponse.Types.Result.Types.Coupon.Types.CouponDetail.Types.Discount.Types.DiscountCondidtion.Types;

namespace Admin.NET.Application;

/// <summary>
/// 收款单基础输入参数
/// </summary>
public class ReceiptBaseInput
{
    /// <summary>
    /// 收款单号
    /// </summary>
    public virtual string? ReceiptNo { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public virtual string? UnitName { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public virtual string? ContractNum { get; set; }

    /// <summary>
    /// 上级单号
    /// </summary>
    public virtual string? SuperiorOrder { get; set; }

    /// <summary>
    /// 单据金额
    /// </summary>
    public virtual decimal? DocumentAmount { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public virtual decimal? AmountReceived { get; set; }

    /// <summary>
    /// 已开票金额
    /// </summary>
    public virtual decimal? InvoicedAmount { get; set; }

    /// <summary>
    /// 收入类型
    /// </summary>
    public virtual string? IncomeType { get; set; }

    /// <summary>
    /// 收入名目
    /// </summary>
    public virtual string? IncomeCategory { get; set; }

    /// <summary>
    /// 收款状态
    /// </summary>
    public virtual PaymentStatusEnum PaymentStatus { get; set; }

    /// <summary>
    /// 交易账户
    /// </summary>
    public virtual long? Trading { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public virtual bool InvoiceStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Notes { get; set; }
    /// <summary>
    /// 摘要
    /// </summary>
    public virtual string? Abstract { get; set; }
    /// <summary>
    /// 发票号码
    /// </summary>
    public virtual string? InvoiceNo { get; set; }
}

/// <summary>
/// 收款单分页查询输入参数
/// </summary>
public class ReceiptInput : BasePageInput
{

    /// <summary>
    /// 创建人姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 收款状态
    /// </summary>
    public string? PaymentStatus { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public string? InvoiceStatus { get; set; }

}

/// <summary>
/// 收款单增加输入参数
/// </summary>
public class AddReceiptInput : ReceiptBaseInput
{
}

/// <summary>
/// 收款单删除输入参数
/// </summary>
public class DeleteReceiptInput : BaseIdInput
{
}

/// <summary>
/// 收款单更新输入参数
/// </summary>
public class UpdateReceiptInput : ReceiptBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 收款单开票信息更新输入参数
/// </summary>
public class ReceiptInvoiceInput
{
    /// <summary>
    /// 单据号
    /// </summary>
    [Required(ErrorMessage = "单据号不能为空")]
    public string djh { get; set; }

    /// <summary>
    /// 发票号码
    /// </summary>
    [Required(ErrorMessage = "发票号码不能为空")]
    public string InvoiceNo { get; set; }

    /// <summary>
    /// 开票金额
    /// </summary>
    [Required(ErrorMessage = "开票金额不能为空")]
    public decimal? InvoicedAmount { get; set; }
}

/// <summary>
/// 收款单主键查询输入参数
/// </summary>
public class QueryByIdReceiptInput : DeleteReceiptInput
{

}
