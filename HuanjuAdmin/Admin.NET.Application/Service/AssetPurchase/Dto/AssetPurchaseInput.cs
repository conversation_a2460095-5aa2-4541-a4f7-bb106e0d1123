﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 固资采购基础输入参数
    /// </summary>
    public class AssetPurchaseBaseInput
    {
        /// <summary>
        /// 固资ID
        /// </summary>
        public virtual long AssetId { get; set; }
        
        /// <summary>
        /// 单价
        /// </summary>
        public virtual decimal? UnitPrice { get; set; }
        
        /// <summary>
        /// 采购数量
        /// </summary>
        public virtual int PurchaseCount { get; set; }
        
        /// <summary>
        /// 入库数量
        /// </summary>
        public virtual int InCount { get; set; }
        
        /// <summary>
        /// 总价
        /// </summary>
        public virtual decimal? TotalPrice { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public virtual long? SupplierId { get; set; }
        
        /// <summary>
        /// 采购时间
        /// </summary>
        public virtual DateTime? PurchaseTime { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
    }

    /// <summary>
    /// 固资采购分页查询输入参数
    /// </summary>
    public class AssetPurchaseInput : BasePageInput
    {
        /// <summary>
        /// 固资ID
        /// </summary>
        public long AssetId { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long? SupplierId { get; set; }
        
        /// <summary>
        /// 采购时间
        /// </summary>
        public DateTime? PurchaseTime { get; set; }
        
        /// <summary>
         /// 采购时间范围
         /// </summary>
         public List<DateTime?> PurchaseTimeRange { get; set; } 
    }

    /// <summary>
    /// 固资采购增加输入参数
    /// </summary>
    public class AddAssetPurchaseInput : AssetPurchaseBaseInput
    {
    }

    /// <summary>
    /// 固资采购删除输入参数
    /// </summary>
    public class DeleteAssetPurchaseInput : BaseIdInput
    {
    }

    /// <summary>
    /// 固资采购更新输入参数
    /// </summary>
    public class UpdateAssetPurchaseInput : AssetPurchaseBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 固资采购主键查询输入参数
    /// </summary>
    public class QueryByIdAssetPurchaseInput : DeleteAssetPurchaseInput
    {

    }
