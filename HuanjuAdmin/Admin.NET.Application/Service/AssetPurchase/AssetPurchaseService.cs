﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Org.BouncyCastle.Cms;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 固资采购服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class AssetPurchaseService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<AssetPurchase> _rep;
    private readonly SqlSugarRepository<AssetInventory> _repInventory;
    public AssetPurchaseService(
        UserManager userManager,
        SqlSugarRepository<AssetPurchase> rep,
        SqlSugarRepository<AssetInventory> repInventory)
    {
        _userManager = userManager;
        _rep = rep;
        _repInventory = repInventory;
    }

    /// <summary>
    /// 分页查询固资采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<AssetPurchaseOutput>> Page(AssetPurchaseInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .WhereIF(input.AssetId > 0, u => u.AssetId == input.AssetId)
                    .WhereIF(input.SupplierId > 0, u => u.SupplierId == input.SupplierId)

                    .Select(u => new AssetPurchaseOutput
                    {
                        Id = u.Id,
                        AssetId = u.AssetId,
                        AssetInventoryName = u.AssetInventory.Name,
                        UnitPrice = u.UnitPrice,
                        PurchaseCount = u.PurchaseCount,
                        InCount = u.InCount,
                        TotalPrice = u.TotalPrice,
                        SupplierId = u.SupplierId,
                        PubSupplierName = u.PubSupplier.Name,
                        PurchaseTime = u.PurchaseTime,
                        Remark = u.Remark,
                    })
;
        if (input.PurchaseTimeRange != null && input.PurchaseTimeRange.Count > 0)
        {
            DateTime? start = input.PurchaseTimeRange[0];
            query = query.WhereIF(start.HasValue, u => u.PurchaseTime > start);
            if (input.PurchaseTimeRange.Count > 1 && input.PurchaseTimeRange[1].HasValue)
            {
                var end = input.PurchaseTimeRange[1].Value.AddDays(1);
                query = query.Where(u => u.PurchaseTime < end);
            }
        }
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加固资采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddAssetPurchaseInput input)
    {
        var entity = input.Adapt<AssetPurchase>();
        await _rep.InsertAsync(entity);

        //同步更新库存，库存总量+=采购数量 库存数量+=入库数量
        var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);
        assetInventory.TotalCount += entity.PurchaseCount;
        assetInventory.InventoryCount += entity.InCount;
        await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除固资采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteAssetPurchaseInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除

        //同步更新库存，库存总量-=采购数量 库存数量-=入库数量
        var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);
        assetInventory.TotalCount -= entity.PurchaseCount;
        assetInventory.InventoryCount -= entity.InCount;
        await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新固资采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateAssetPurchaseInput input)
    {
        var oringEntity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        var entity = input.Adapt<AssetPurchase>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        if (oringEntity.PurchaseCount != entity.PurchaseCount || oringEntity.InCount != entity.InCount)
        {
            //增减数量
            var totalChagedCount = entity.PurchaseCount - oringEntity.PurchaseCount;
            var inventoryChangeCount = entity.InCount - oringEntity.InCount;

            //同步更新库存，库存总量+=采购数量 库存数量+=入库数量
            var assetInventory = await _repInventory.GetFirstAsync(u => u.Id == entity.AssetId && u.IsDelete == false);
            assetInventory.TotalCount += totalChagedCount;
            assetInventory.InventoryCount += inventoryChangeCount;
            await _repInventory.AsUpdateable(assetInventory).UpdateColumns(u => new { u.TotalCount, u.InventoryCount }).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取固资采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<AssetPurchase> Get([FromQuery] QueryByIdAssetPurchaseInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取固资采购列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<AssetPurchaseOutput>> List([FromQuery] AssetPurchaseInput input)
    {
        return await _rep.AsQueryable().Select<AssetPurchaseOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取固资ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "AssetInventoryDropdown"), HttpGet]
    public async Task<dynamic> AssetInventoryDropdown()
    {
        return await _rep.Context.Queryable<AssetInventory>()
                .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id,

                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取供应商ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "PubSupplierDropdown"), HttpGet]
    public async Task<dynamic> PubSupplierDropdown()
    {
        return await _rep.Context.Queryable<PubSupplier>()
                .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                .WhereIF(!_userManager.SuperAdmin && !_userManager.Admin, u => u.IsCommunal || (!u.IsCommunal && u.OrgId == _userManager.OrgId))
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }

}

