﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 进销存汇总基础输入参数
    /// </summary>
    public class wareInventorychangesBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 商品Id
        /// </summary>
        public virtual long? goodsId { get; set; }
        
        /// <summary>
        /// 期初结存数量
        /// </summary>
        public virtual decimal? OpeningNum { get; set; }
        
        /// <summary>
        /// 期初结存金额
        /// </summary>
        public virtual decimal? OpeningMoney { get; set; }
        
        /// <summary>
        /// 本期出库数量
        /// </summary>
        public virtual decimal? CurrentNum { get; set; }
        
        /// <summary>
        /// 本期出库金额
        /// </summary>
        public virtual decimal? CurrentMoney { get; set; }
        
        /// <summary>
        /// 本期结存数量
        /// </summary>
        public virtual decimal? BalanceNum { get; set; }
        
        /// <summary>
        /// 本期结存金额
        /// </summary>
        public virtual decimal? BalanceMoney { get; set; }
        
    }

    /// <summary>
    /// 进销存汇总分页查询输入参数
    /// </summary>
    public class wareInventorychangesInput : BasePageInput
    {
        //public Datetime MyProperty { get; set; }
    }

    /// <summary>
    /// 进销存汇总增加输入参数
    /// </summary>
    public class AddwareInventorychangesInput : wareInventorychangesBaseInput
    {
    }

    /// <summary>
    /// 进销存汇总删除输入参数
    /// </summary>
    public class DeletewareInventorychangesInput : BaseIdInput
    {
    }

    /// <summary>
    /// 进销存汇总更新输入参数
    /// </summary>
    public class UpdatewareInventorychangesInput : wareInventorychangesBaseInput
    {
    }

    /// <summary>
    /// 进销存汇总主键查询输入参数
    /// </summary>
    public class QueryByIdwareInventorychangesInput : DeletewareInventorychangesInput
    {

    }
