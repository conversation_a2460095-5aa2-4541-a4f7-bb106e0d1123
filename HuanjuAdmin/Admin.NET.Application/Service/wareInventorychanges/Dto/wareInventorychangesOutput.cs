﻿namespace Admin.NET.Application;

/// <summary>
/// 进销存汇总输出参数
/// </summary>
public class wareInventorychangesOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 商品Id
    /// </summary>
    public long? goodsId { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string GoodName { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string GoodCode { get; set; }

    /// <summary>
    /// 期初结存数量
    /// </summary>
    public decimal? OpeningNum { get; set; }

    /// <summary>
    /// 期初结存金额
    /// </summary>
    public decimal? OpeningMoney { get; set; }

    /// <summary>
    /// 本期出库数量
    /// </summary>
    public decimal? CurrentNum { get; set; }

    /// <summary>
    /// 本期出库金额
    /// </summary>
    public decimal? Current<PERSON>oney { get; set; }

    /// <summary>
    /// 本期结存数量
    /// </summary>
    public decimal? BalanceNum { get; set; }

    /// <summary>
    /// 本期结存金额
    /// </summary>
    public decimal? BalanceMoney { get; set; }

}


