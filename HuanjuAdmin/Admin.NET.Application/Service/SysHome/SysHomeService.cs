﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using SqlSugar;
using System;
using System.Linq;
namespace Admin.NET.Application;
/// <summary>
/// 系统首页服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SysHomeService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SysMenuService _sysMenuService;
    private readonly SqlSugarRepository<WarehouseInrecord> _warehouseinrecord;
    private readonly SqlSugarRepository<Receipt> _receipt;
    private readonly SqlSugarRepository<Paymentorder> _paymentOrder;
    private readonly SqlSugarRepository<WarehouseStore> _warehouseStore;
    private readonly SqlSugarRepository<Warehouseout> _warehouseOut;
    private readonly SqlSugarRepository<Salescontract> _salescontract;
    private readonly SqlSugarRepository<warerevenue> _warehouseRevenue;
    private readonly SqlSugarRepository<OutboundRecord> _outboundRecord;

    public SysHomeService(
       UserManager userManager,
       SysMenuService sysMenuService,
       SqlSugarRepository<WarehouseInrecord> warehouseinrecord,
       SqlSugarRepository<Receipt> receipt,
       SqlSugarRepository<Paymentorder> paymentOrder,
       SqlSugarRepository<WarehouseStore> warehouseStore,
       SqlSugarRepository<Warehouseout> warehouseOut,
       SqlSugarRepository<Salescontract> salescontract,
       SqlSugarRepository<warerevenue> warehouseRevenue,
       SqlSugarRepository<OutboundRecord> outboundRecord)
    {
        _userManager = userManager;
        _sysMenuService = sysMenuService;
        _warehouseinrecord = warehouseinrecord;
        _receipt = receipt;
        _paymentOrder = paymentOrder;
        _warehouseStore = warehouseStore;
        _warehouseOut = warehouseOut;
        _salescontract = salescontract;
        _warehouseRevenue = warehouseRevenue;
        _outboundRecord = outboundRecord;
    }
    /// <summary>
    /// 获取首页统计数据
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Statistics")]
    public async Task<HomeStatisticsOutput> GetStatistics()
    {
        // 获取拥有按钮权限集合
        var buttons = await _sysMenuService.GetOwnBtnPermList();
        var today = DateTime.Now.Date;

        var hoemStatistics = new HomeStatisticsOutput
        {
            //待入库
            PendingInbound = buttons.Contains("warehouseInrecord:page") ? (await _warehouseinrecord.CountAsync(u => u.InhouseStatus == RcvStatusEnum.NotReceived || u.InhouseStatus == RcvStatusEnum.Tobestored || u.InhouseStatus == RcvStatusEnum.PartiallyReceived)).ToString() : "/",

            //待出库
            PendingOutbound = buttons.Contains("warehouseout:page") ? (await _warehouseOut.CountAsync(u => u.OutboundStatus == 0 || u.OutboundStatus == 1 || u.OutboundStatus == 2)).ToString() : "/",

            //待收款
            PendingReceipt = buttons.Contains("receipt:page") ? (await _receipt.CountAsync(u => u.PaymentStatus == 0 || u.PaymentStatus == 1 || u.PaymentStatus == 2)).ToString() : "/",

            //待付款
            PendingPayment = buttons.Contains("paymentOrder:page") ? (await _paymentOrder.CountAsync(u => u.PaymentStatus == 0 || u.PaymentStatus == 1 || u.PaymentStatus == 2)).ToString() : "/",

            //待开票
            PendingInvoice = buttons.Contains("receipt:page") ? (await _receipt.CountAsync(u => u.InvoiceStatus == 1 || u.InvoiceStatus == 3)).ToString() : "/",

            //缺货
            PendingStockOut = buttons.Contains("warehouseStore:page") ? (await _warehouseStore.CountAsync(u => u.StockOrNot == true)).ToString() : "/",

            //临期/到期
            ExpiryWarning = buttons.Contains("warehouseStore:page") ? (await _warehouseStore.CountAsync(u => u.ExpiredWarning > 0)).ToString() : "/",

            //库存预警
            StockWarning = buttons.Contains("warehouseStore:page") ? (await _warehouseStore.CountAsync(u => u.StockWarning > 0)).ToString() : "/",

            //待履约
            PendingSalesZX = buttons.Contains("salesContract:page") ?
                (await _salescontract.AsQueryable()
                    .Where(x => x.ContractStatus == 2 || x.ContractStatus == 3)
                    .Where(x => SqlFunc.Subqueryable<SalesperFormanceplan>()
                        .Where(p => p.SalesOrder == x.Id.ToString() && p.Status == 0 && p.PlanTime.ToDateTime().Date < today)
                        .Any())
                    .ToListAsync()).Count.ToString() : "/",

            //待签约
            PendingSalesGJ = buttons.Contains("salesContract:page") ? (await _salescontract.CountAsync(u => u.ContractStatus == 0)).ToString() : "/",

        };
        return hoemStatistics;
    }


    /// <summary>
    /// 首页统计数据返回结构
    /// </summary>
    public class HomeStatisticsOutput
    {
        /// <summary>
        /// 待入库数量
        /// </summary>
        public string PendingInbound { get; set; }

        /// <summary>
        /// 待收款数量
        /// </summary>
        public string PendingReceipt { get; set; }

        /// <summary>
        /// 待付款数量
        /// </summary>
        public string PendingPayment { get; set; }

        /// <summary>
        /// 待开票申请数量
        /// </summary>
        public string PendingInvoice { get; set; }

        /// <summary>
        /// 销售待执行数量
        /// </summary>
        public string PendingSalesZX { get; set; }

        /// <summary>
        /// 缺货数量
        /// </summary>
        public string PendingStockOut { get; set; }

        /// <summary>
        /// 临期/到期数量
        /// </summary>
        public string ExpiryWarning { get; set; }

        /// <summary>
        /// 库存预警数量
        /// </summary>
        public string StockWarning { get; set; }

        /// <summary>
        /// 待出库数量
        /// </summary>
        public string PendingOutbound { get; set; }

        /// <summary>
        /// 销售待跟进数量
        /// </summary>
        public string PendingSalesGJ { get; set; }
    }

    /// <summary>
    /// 首页推荐数据返回结构
    /// </summary>
    public class HomeRecommendOutput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 总额
        /// </summary>
        public string Total { get; set; }

        /// <summary>
        /// 月数据
        /// </summary>
        public string Month { get; set; }

        /// <summary>
        /// 年数据
        /// </summary>
        public string Year { get; set; }
    }

    /// <summary>
    /// 获取首页推荐数据
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Recommend")]
    public async Task<List<HomeRecommendOutput>> GetRecommend()
    {
        var thisYear = DateTime.Now.Year;
        var thisMonth = DateTime.Now.Month;

        // 签约金额（元）
        var monthCustomers = (await _salescontract.AsQueryable()
            .Where(u => u.ContractStatus > 1 && u.SigningTime.HasValue && u.SigningTime.Value.Year == thisYear && u.SigningTime.Value.Month == thisMonth)
            .SumAsync(u => u.ContractAmount))
            .ToString("#,##0");

        var yearCustomers = (await _salescontract.AsQueryable()
            .Where(u => u.ContractStatus > 1 && u.SigningTime.HasValue && u.SigningTime.Value.Year == thisYear)
            .SumAsync(u => u.ContractAmount))
            .ToString("#,##0");

        // 回款金额（元）
        var monthOrders = (await _warehouseRevenue.AsQueryable()
            .Where(u => u.revenueType == 1 && u.revenueTime.HasValue && u.revenueTime.Value.Year == thisYear && u.revenueTime.Value.Month == thisMonth)
            .SumAsync(u => u.Incomeamount));
        var formattedMonthOrders = ((decimal?)monthOrders ?? 0).ToString("#,##0");

        var yearOrders = await _warehouseRevenue.AsQueryable()
            .Where(u => u.revenueType == 1 && u.revenueTime.HasValue && u.revenueTime.Value.Year == thisYear)
            .SumAsync(u => u.Incomeamount);
        var formattedYearOrders = ((decimal?)yearOrders ?? 0).ToString("#,##0");

        // 待回款金额（元）
        var salesAmount = await _receipt.AsQueryable()
            .Where(u => u.PaymentStatus == 1 || u.PaymentStatus == 2)
            .SumAsync(u => (u.DocumentAmount ?? 0) - (u.AmountReceived ?? 0));
        var formattedAmount = ((decimal?)salesAmount ?? 0).ToString("#,##0");

        return new List<HomeRecommendOutput>
        {
            new HomeRecommendOutput
            {
                Title = "签约金额（元）",
                Type = "normal",
                Month = monthCustomers,
                Year = yearCustomers
            },
            new HomeRecommendOutput
            {
                Title = "回款金额（元）",
                Type = "normal",
                Month = formattedMonthOrders,
                Year = formattedYearOrders
            },
            new HomeRecommendOutput
            {
                Title = "待回款金额（元）",
                Type = "amount",
                Total = $"¥ {formattedAmount}"
            }
        };
    }

    /// <summary>
    /// 销售趋势数据返回结构
    /// </summary>
    public class SalesTrendResponse
    {
        /// <summary>
        /// 时间轴数据
        /// </summary>
        public List<string> XAxis { get; set; }

        /// <summary>
        /// 销售趋势数据列表
        /// </summary>
        public List<SalesTrendItem> Series { get; set; }
    }

    /// <summary>
    /// 销售趋势数据项
    /// </summary>
    public class SalesTrendItem
    {
        /// <summary>
        /// 商品名称
        /// </summary>
        public string GoodsName { get; set; }

        /// <summary>
        /// 销售数据
        /// </summary>
        public List<decimal> Data { get; set; }
    }

    /// <summary>
    /// 获取销售趋势数据
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "SalesTrend")]
    public async Task<SalesTrendResponse> GetSalesTrend([FromQuery] SalesTrendInput input)
    {
        var now = DateTime.Now;
        var result = new SalesTrendResponse
        {
            XAxis = new List<string>(),
            Series = new List<SalesTrendItem>()
        };

        if (input.TimeRange == "month")
        {
            var daysInMonth = DateTime.DaysInMonth(now.Year, now.Month);

            // 设置X轴数据
            result.XAxis = Enumerable.Range(1, daysInMonth).Select(i => $"{i}日").ToList();

            if (input.Type == "quantity") //销售量
            {
                // 获取当月所有商品的销售数据
                var salesData = await _outboundRecord.AsQueryable()
                    .InnerJoin<WarehouseoutMX>((u, w) => u.WarehouseOutMxId == w.Id)
                    .InnerJoin<Warehousegoods>((u, w, g) => w.goodsId == g.Id)
                    .InnerJoin<Warehouseout>((u, w, g, o) => o.Id == w.OutId)
                    .Where((u, w, g, o) => u.CreateTime.HasValue &&
                        u.CreateTime.Value.Year == now.Year &&
                        u.CreateTime.Value.Month == now.Month && o.Outboundtype == 0)//销售出库
                    .Select((u, w, g, o) => new
                    {
                        u.CreateTime,
                        g.Name,
                        u.OutBoundCount
                    })
                    .ToListAsync();

                // 按商品分组并只取前7个销量最高的商品
                var groupedData = salesData.GroupBy(x => x.Name)
                    .Select(g => new
                    {
                        Name = g.Key,
                        TotalSales = g.Sum(x => x.OutBoundCount ?? 0)
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .Take(7)
                    .Select(x => x.Name);

                foreach (var goodsName in groupedData)
                {
                    var group = salesData.Where(x => x.Name == goodsName);
                    var trendItem = new SalesTrendItem
                    {
                        GoodsName = goodsName,
                        Data = new List<decimal>()
                    };

                    // 填充每天的数据
                    for (int day = 1; day <= daysInMonth; day++)
                    {
                        var dailyAmount = group
                            .Where(x => x.CreateTime.Value.Day == day)
                            .Sum(x => x.OutBoundCount ?? 0);
                        trendItem.Data.Add(dailyAmount);
                    }

                    result.Series.Add(trendItem);
                }
            }
            else // amount 销售额
            {
                // 获取当月所有商品的出库数据
                var outboundData = await _outboundRecord.AsQueryable()
                    .InnerJoin<WarehouseoutMX>((u, w) => u.WarehouseOutMxId == w.Id)
                    .InnerJoin<Warehousegoods>((u, w, g) => w.goodsId == g.Id)
                    .InnerJoin<Warehouseout>((u, w, g, o) => o.Id == w.OutId)
                    .Where((u, w, g, o) => u.CreateTime.HasValue &&
                        u.CreateTime.Value.Year == now.Year &&
                        u.CreateTime.Value.Month == now.Month && o.Outboundtype == 0)//销售出库
                    .Select((u, w, g, o) => new
                    {
                        u.CreateTime,
                        g.Name,
                        V = u.OutBoundCount * w.Unitprice
                    })
                    .ToListAsync();

                // 按商品分组并只取前7个销售额最高的商品
                var groupedData = outboundData.GroupBy(x => x.Name)
                    .Select(g => new
                    {
                        Name = g.Key,
                        TotalAmount = g.Sum(x => x.V ?? 0)
                    })
                    .OrderByDescending(x => x.TotalAmount)
                    .Take(7)
                    .Select(x => x.Name);

                foreach (var goodsName in groupedData)
                {
                    var group = outboundData.Where(x => x.Name == goodsName);
                    var trendItem = new SalesTrendItem
                    {
                        GoodsName = goodsName,
                        Data = new List<decimal>()
                    };

                    // 填充每天的数据
                    for (int day = 1; day <= daysInMonth; day++)
                    {
                        var dailyQuantity = group
                            .Where(x => x.CreateTime.Value.Day == day)
                            .Sum(x => x.V ?? 0);
                        trendItem.Data.Add(dailyQuantity);
                    }

                    result.Series.Add(trendItem);
                }
            }
        }
        else // year
        {
            // 设置X轴数据
            result.XAxis = Enumerable.Range(1, 12).Select(i => $"{i}月").ToList();

            if (input.Type == "quantity") //销售量
            {
                // 获取当年所有商品的销售数据
                var salesData = await _outboundRecord.AsQueryable()
                    .InnerJoin<WarehouseoutMX>((u, w) => u.WarehouseOutMxId == w.Id)
                    .InnerJoin<Warehousegoods>((u, w, g) => w.goodsId == g.Id)
                    .InnerJoin<Warehouseout>((u, w, g, o) => o.Id == w.OutId)
                    .Where((u, w, g, o) => u.CreateTime.HasValue &&
                        u.CreateTime.Value.Year == now.Year && o.Outboundtype == 0)//销售出库
                    .Select((u, w, g, o) => new
                    {
                        u.CreateTime,
                        g.Name,
                        u.OutBoundCount
                    })
                    .ToListAsync();

                // 按商品分组并只取前7个销量最高的商品
                var groupedData = salesData.GroupBy(x => x.Name)
                    .Select(g => new
                    {
                        Name = g.Key,
                        TotalSales = g.Sum(x => x.OutBoundCount ?? 0)
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .Take(7)
                    .Select(x => x.Name);

                foreach (var goodsName in groupedData)
                {
                    var group = salesData.Where(x => x.Name == goodsName);
                    var trendItem = new SalesTrendItem
                    {
                        GoodsName = goodsName,
                        Data = new List<decimal>()
                    };

                    // 填充每月的数据
                    for (int month = 1; month <= 12; month++)
                    {
                        var monthlyAmount = group
                            .Where(x => x.CreateTime.Value.Month == month)
                            .Sum(x => x.OutBoundCount ?? 0);
                        trendItem.Data.Add(monthlyAmount);
                    }

                    result.Series.Add(trendItem);
                }
            }
            else // amount 销售额
            {
                // 获取当年所有商品的出库数据
                var outboundData = await _outboundRecord.AsQueryable()
                    .InnerJoin<WarehouseoutMX>((u, w) => u.WarehouseOutMxId == w.Id)
                    .InnerJoin<Warehousegoods>((u, w, g) => w.goodsId == g.Id)
                    .InnerJoin<Warehouseout>((u, w, g, o) => o.Id == w.OutId)
                    .Where((u, w, g, o) => u.CreateTime.HasValue &&
                        u.CreateTime.Value.Year == now.Year &&
                        u.CreateTime.Value.Month == now.Month && o.Outboundtype == 0)//销售出库
                    .Select((u, w, g, o) => new
                    {
                        u.CreateTime,
                        g.Name,
                        V = u.OutBoundCount * w.Unitprice
                    })
                    .ToListAsync();

                // 按商品分组并只取前7个销售额最高的商品
                var groupedData = outboundData.GroupBy(x => x.Name)
                    .Select(g => new
                    {
                        Name = g.Key,
                        TotalAmount = g.Sum(x => x.V ?? 0)
                    })
                    .OrderByDescending(x => x.TotalAmount)
                    .Take(7)
                    .Select(x => x.Name);

                foreach (var goodsName in groupedData)
                {
                    var group = outboundData.Where(x => x.Name == goodsName);
                    var trendItem = new SalesTrendItem
                    {
                        GoodsName = goodsName,
                        Data = new List<decimal>()
                    };

                    // 填充每月的数据
                    for (int month = 1; month <= 12; month++)
                    {
                        var monthlyQuantity = group
                            .Where(x => x.CreateTime.Value.Month == month)
                            .Sum(x => x.V ?? 0);
                        trendItem.Data.Add(monthlyQuantity);
                    }

                    result.Series.Add(trendItem);
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 销售趋势查询参数
    /// </summary>
    public class SalesTrendInput
    {
        /// <summary>
        /// 数据类型：amount(金额)/quantity(数量)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 时间范围：month(月)/year(年)
        /// </summary>
        public string TimeRange { get; set; }
    }

}