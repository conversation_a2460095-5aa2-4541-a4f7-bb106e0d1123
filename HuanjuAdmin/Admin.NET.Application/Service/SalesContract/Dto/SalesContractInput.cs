﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 销售合约基础输入参数
/// </summary>
public class SalesContractBaseInput
{

    /// <summary>
    /// 主键
    /// </summary>
    public virtual long Id { get; set; }
    /// <summary>
    /// 销售人
    /// </summary>
    public virtual string Salesperson { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    public virtual string SalesOrder { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public virtual string ContractCode { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public virtual int ContractStatus { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public virtual decimal TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public virtual decimal DiscountAmt { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public virtual decimal ContractAmount { get; set; }

    /// <summary>
    /// 签订时间
    /// </summary>
    public virtual DateTime? SigningTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public virtual DateTime? EndTime { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public virtual long CustomerId { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public virtual string? CustomerName { get; set; }
    /// <summary>
    /// 商品信息
    /// </summary>
    public virtual string? GoodsInfo { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public virtual string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public virtual string? Tel { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public virtual string? Address { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public virtual decimal? AmountReceived { get; set; }

    /// <summary>
    /// 已开票金额
    /// </summary>
    public virtual decimal? InvoicedAmount { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public virtual bool? OutOfStock { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Notes { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    public virtual string? Annex { get; set; }

}

/// <summary>
/// 销售合约分页查询输入参数
/// </summary>
public class SalesContractInput : BasePageInput
{
    /// <summary>
    /// 销售人
    /// </summary>
    public string Salesperson { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    public string SalesOrder { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public string ContractCode { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public string? ContractStatus { get; set; }

    /// <summary>
    /// 签订时间
    /// </summary>
    public DateTime? SigningTime { get; set; }

    /// <summary>
    /// 签订时间范围
    /// </summary>
    public List<DateTime?> SigningTimeRange { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }
    /// <summary>
    /// 商品信息
    /// </summary>
    public string? GoodsInfo { get; set; }

}

/// <summary>
/// 销售合约增加输入参数
/// </summary>
public class AddSalesContractInput //: SalesContractBaseInput
{
    public SalesContractBaseInput addSalesContractBaseInput { get; set; }
    public List<SaleOfGoodsInput> listMx { get; set; }
}

/// <summary>
/// 销售合约删除输入参数
/// </summary>
public class DeleteSalesContractInput : BaseIdInput
{
}

/// <summary>
/// 销售合约更新输入参数
/// </summary>
public class UpdateSalesContractInput : SalesContractBaseInput
{
}

/// <summary>
/// 销售合约主键查询输入参数
/// </summary>
public class QueryByIdSalesContractInput : DeleteSalesContractInput
{

}
