﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.SalesAnalysis.Dto;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Nest;
using NPOI.HSSF.UserModel;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;

namespace Admin.NET.Application.Service.SalesAnalysis;

[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SalesAnalysisService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<SaleOfGoods> _rep;
    public SalesAnalysisService(SqlSugarRepository<SaleOfGoods> rep, ISqlSugarClient db)
    {
        _rep = rep;
        _db = db;
    }

    /// <summary>
    /// 分页查询商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
//    [HttpPost]
//    [ApiDescriptionSettings(Name = "Page")]
//    public async Task<SqlSugarPagedList<SalesAnalysisResDto>> Page(SalesAnalysisReqDto input)
//    {
//        var sta = await _reps.AsQueryable().Select(x=> SqlFunc.AggregateSum(x.ContractAmount)).FirstAsync();
//        //var query1 = _db.Queryable<SaleOfGoods>().LeftJoin<Salescontract>((c, u) => c.SalesOrder == u.SalesOrder)
//        //    .LeftJoin<WarehouseStore>((c, u, us) => c.goodsId == us.TradeID).Where(c => c.Id == 1).Select((c, u, us) =>
//        //    new { CompanyId = c.Id, Name = c.TradeName, ContractNum = c., ContractAmount = u.ContractAmount, CurrentCost = us.CurrentCost })
//        //   .GroupBy(it => new { it.ContractAmount, it.Name })
//        //   //.Having(it => SqlFunc.AggregateAvg(it.Id) > 0)
//        //   .Select(it => new SalesAnalysisResDto { Name =it.Name , Turnover = SqlFunc.AggregateSum(it.ContractAmount),
//        //    Transaction= (SqlFunc.AggregateSum(it.ContractAmount) / sta).ToString(),
//        //    Amountofprofit= (SqlFunc.AggregateSum(it.ContractAmount)- SqlFunc.AggregateSum(it.CurrentCost)).ToString(),
//        //    Profitproportion= (SqlFunc.AggregateSum(it.ContractAmount) - SqlFunc.AggregateSum(it.CurrentCost)).ToString(),
//        //   });
//        //{
//        //Name = x.TradeName,
//        //Turnover = s.Where(s => s.SalesOrder == x.SalesOrder).Sum(s => s.ContractAmount),
//        //Transaction = (s.Where(s => s.SalesOrder == x.SalesOrder).Sum(s => s.ContractAmount) / s.Sum(s => s.ContractAmount)).ToString(),
//        //Amountofprofit = (s.Where(s => s.SalesOrder == x.SalesOrder).Sum(s => s.ContractAmount) / SqlFunc.AggregateSum(x.ContractNum) / Store.Where(s => s.TradeName == x.TradeName).First().CurrentCost).ToString(),
//        //Profitproportion = (s.Where(s => s.SalesOrder == x.SalesOrder).Sum(s => s.ContractAmount) / SqlFunc.AggregateSum(x.ContractNum) / Store.Where(s => s.TradeName == x.TradeName).First().CurrentCost).ToString()
//        //        switch (input.Type)
//        //        {
//        //            case Enum.SalesAnalysisType.Commodity:
//        //                query = _rep.AsQueryable()
//        //            .Where(u => u.CreateTime > input.BegionTime && u.CreateTime < input.EndTime)
//        //            .Select(x => new SalesAnalysisResDto { Name="商品", Turnover= });
//        //                break;
//        //            case Enum.SalesAnalysisType.Custom:
//        //                query = query
//        //.Where(u => u.CreateTime > input.BegionTime && u.CreateTime < input.EndTime)
//        //.Select(x => new SalesAnalysisResDto { Name });
//        //                break;
//        //            case Enum.SalesAnalysisType.Sale:
//        //                query = query
//        //.Where(u => u.CreateTime > input.BegionTime && u.CreateTime < input.EndTime)
//        //.Select(x => new SalesAnalysisResDto { Name });
//        //                break;
//        //            default:
//        //                break;
//        //        }

//        query1 = query1.OrderBuilder(input);
//        return await query1.ToPagedListAsync(input.Page, input.PageSize);
//}

}
