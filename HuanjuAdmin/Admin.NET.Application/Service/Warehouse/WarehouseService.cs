﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;

namespace Admin.NET.Application;
/// <summary>
/// 仓库信息服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseService : IDynamicApiController, ITransient
{
    UserManager _userManager;
    private readonly SqlSugarRepository<Warehouse> _rep;
    public WarehouseService(SqlSugarRepository<Warehouse> rep, UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询仓库信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseOutput>> Page(WarehouseInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Manager), u => u.Manager.Contains(input.Manager.Trim()))

                    .Select<WarehouseOutput>()
;
        query = query.OrderBuilder(input, "OrderNo", false);
        var list = await query.ToPagedListAsync(input.Page, input.PageSize);
        return list;
    }

    /// <summary>
    /// 增加仓库信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseInput input)
    {
        await _rep.AsUpdateable().SetColumns(it => it.Name == it.Name).SetColumnsIF(input.IsDefault == 1, x => x.IsDefault == 0).Where(x => x.TenantId == _userManager.TenantId && x.IsDefault == 1).ExecuteCommandAsync();
        var entity = input.Adapt<Warehouse>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除仓库信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新仓库信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseInput input)
    {
        await _rep.AsUpdateable().SetColumns(it => it.Name == it.Name).SetColumnsIF(input.IsDefault == 1, x => x.IsDefault == 0).Where(x => x.TenantId == _userManager.TenantId && x.IsDefault == 1).ExecuteCommandAsync();
        var entity = input.Adapt<Warehouse>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取仓库信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<Warehouse> Get([FromQuery] QueryByIdWarehouseInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取仓库信息列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseOutput>> List([FromQuery] WarehouseInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取仓库编号列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WarehouseDropdown"), HttpGet]
    public async Task<dynamic> WarehouseDropdown()
    {
        return await _rep.Context.Queryable<Warehouse>()
            .Where(x => x.TenantId == _userManager.TenantId)
                .Where(x => x.IsDelete == false && x.Status == 1)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }



}

