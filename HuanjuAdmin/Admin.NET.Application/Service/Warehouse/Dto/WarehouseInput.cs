﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 仓库信息基础输入参数
/// </summary>
public class WarehouseBaseInput
{
    /// <summary>
    /// 仓库名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    public virtual string? Code { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public virtual StatusEnum Status { get; set; }

    /// <summary>
    /// 是否默认仓库
    /// </summary>
    public virtual int IsDefault { get; set; }

    /// <summary>
    /// 管理员
    /// </summary>
    public virtual string? Manager { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public virtual string? Address { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

}

/// <summary>
/// 仓库信息分页查询输入参数
/// </summary>
public class WarehouseInput : BasePageInput
{
    /// <summary>
    /// 仓库名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 仓库编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 管理员
    /// </summary>
    public string? Manager { get; set; }

}

/// <summary>
/// 仓库信息增加输入参数
/// </summary>
public class AddWarehouseInput : WarehouseBaseInput
{
}

/// <summary>
/// 仓库信息删除输入参数
/// </summary>
public class DeleteWarehouseInput : BaseIdInput
{
}

/// <summary>
/// 仓库信息更新输入参数
/// </summary>
public class UpdateWarehouseInput : WarehouseBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 仓库信息主键查询输入参数
/// </summary>
public class QueryByIdWarehouseInput : DeleteWarehouseInput
{

}
