﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 个人审批明细输出参数
/// </summary>
public class WorkflowRecordOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 审批单ID
    /// </summary>
    public long OrderId { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 步骤编号
    /// </summary>
    public int StepNumber { get; set; }

    /// <summary>
    /// 审批人
    /// </summary>
    public long Approver { get; set; }

    /// <summary>
    /// 审批时间
    /// </summary>
    public DateTime? ApproveTime { get; set; }

    /// <summary>
    /// 用户
    /// </summary>
    public string SysUserRealName { get; set; }

    /// <summary>
    /// 机构
    /// </summary>
    public string SysOrgName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string SysPosName { get; set; }

    /// <summary>
    /// 审批状态
    /// </summary>
    public ApproveStatusEnum Status { get; set; }

    /// <summary>
    /// 审批意见
    /// </summary>
    public string? Remark { get; set; }

}


