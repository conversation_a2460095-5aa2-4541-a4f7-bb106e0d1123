﻿using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;

/// <summary>
/// 加工单基础输入参数
/// </summary>
public class ProcessOrderBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long Id { get; set; }

    /// <summary>
    /// 加工单号
    /// </summary>
    public virtual string OrderNo { get; set; }

    /// <summary>
    /// 加工单方案Id
    /// </summary>
    public virtual long SchemeId { get; set; }

    /// <summary>
    /// 原料仓库
    /// </summary>
    public virtual long MaterialWarehouseId { get; set; }

    /// <summary>
    /// 成品仓库
    /// </summary>
    public virtual long ProduceWarehouseId { get; set; }

}

/// <summary>
/// 加工单分页查询输入参数
/// </summary>
public class ProcessOrderInput : BasePageInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 加工单号
    /// </summary>
    public string OrderNo { get; set; }

    /// <summary>
    /// 加工单方案Id
    /// </summary>
    public long SchemeId { get; set; }

    /// <summary>
    /// 原料仓库
    /// </summary>
    public long MaterialWarehouseId { get; set; }

    /// <summary>
    /// 成品仓库
    /// </summary>
    public long ProduceWarehouseId { get; set; }

}

/// <summary>
/// 加工单增加输入参数
/// </summary>
public class AddProcessOrderInput : ProcessOrderBaseInput
{
    /// <summary>
    /// 原料列表
    /// </summary>
    public List<ProcessOrderMaterial> MaterialList { get; set; }

    /// <summary>
    /// 成品列表
    /// </summary>
    public List<ProcessOrderProduce> ProduceList { get; set; }
}

/// <summary>
/// 加工单删除输入参数
/// </summary>
public class DeleteProcessOrderInput : BaseIdInput
{
}

/// <summary>
/// 加工单更新输入参数
/// </summary>
public class UpdateProcessOrderInput : ProcessOrderBaseInput
{
    /// <summary>
    /// 原料列表
    /// </summary>
    public List<ProcessOrderMaterial> MaterialList { get; set; }

    /// <summary>
    /// 成品列表
    /// </summary>
    public List<ProcessOrderProduce> ProduceList { get; set; }
}

/// <summary>
/// 加工单主键查询输入参数
/// </summary>
public class QueryByIdProcessOrderInput : DeleteProcessOrderInput
{

}
