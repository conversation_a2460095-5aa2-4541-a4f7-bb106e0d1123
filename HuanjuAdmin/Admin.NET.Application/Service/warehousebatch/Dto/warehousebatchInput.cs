﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 批次表基础输入参数
/// </summary>
public class warehousebatchBaseInput
{
    /// <summary>
    /// 库存ID
    /// </summary>
    public virtual long InventoryId { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public virtual string Batchnumber { get; set; }

    /// <summary>
    /// 良品数量
    /// </summary>
    public virtual int? GoodProductNum { get; set; }

    /// <summary>
    /// 次品数量
    /// </summary>
    public virtual int? RejectNum { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProduceTime { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public virtual int WarrantyTime { get; set; }

    /// <summary>
    /// 到期时间
    /// </summary>
    public virtual DateTime ExpirationTime { get; set; }

    /// <summary>
    /// 成本
    /// </summary>
    public virtual decimal? Cost { get; set; }

    /// <summary>
    /// 过期预警（天）
    /// </summary>
    public virtual int? ExpiryReminder { get; set; }

    /// <summary>
    /// 保质期状态（-1 未设置 0-正常 1-临期 2-过期）
    /// </summary>
    public int? ShelflifeStatus { get; set; }

}

/// <summary>
/// 批次表分页查询输入参数
/// </summary>
public class warehousebatchInput : BasePageInput
{
    /// <summary>
    /// 库存ID
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 库存ID
    /// </summary>
    public long InventoryId { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string Batchnumber { get; set; }

    /// <summary>
    /// 良品数量
    /// </summary>
    public int? GoodProductNum { get; set; }

    /// <summary>
    /// 次品数量
    /// </summary>
    public int? RejectNum { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProduceTime { get; set; }

    /// <summary>
    /// 生产日期范围
    /// </summary>
    public List<DateTime?> ProduceTimeRange { get; set; }
    /// <summary>
    /// 保质期
    /// </summary>
    public int WarrantyTime { get; set; }

    /// <summary>
    /// 到期时间
    /// </summary>
    public DateTime ExpirationTime { get; set; }

    /// <summary>
    /// 到期时间范围
    /// </summary>
    public List<DateTime?> ExpirationTimeRange { get; set; }
    /// <summary>
    /// 成本
    /// </summary>
    public decimal? Cost { get; set; }

}

/// <summary>
/// 批次表增加输入参数
/// </summary>
public class AddwarehousebatchInput : warehousebatchBaseInput
{
}

/// <summary>
/// 批次表删除输入参数
/// </summary>
public class DeletewarehousebatchInput : BaseIdInput
{
}

/// <summary>
/// 批次表更新输入参数
/// </summary>
public class UpdatewarehousebatchInput : warehousebatchBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 批次表主键查询输入参数
/// </summary>
public class QueryByIdwarehousebatchInput : DeletewarehousebatchInput
{

}
