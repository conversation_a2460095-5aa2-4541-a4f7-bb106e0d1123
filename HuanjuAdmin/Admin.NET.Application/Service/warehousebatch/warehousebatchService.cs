﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 批次表服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehousebatchService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<warehousebatch> _rep;
    public WarehousebatchService(SqlSugarRepository<warehousebatch> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询批次表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<warehousebatchOutput>> Page(warehousebatchInput input)
    {
        input.InventoryId = input.Id;
        var query= _rep.AsQueryable()
                    .WhereIF(input.InventoryId>0, u => u.InventoryId == input.InventoryId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Batchnumber), u => u.Batchnumber.Contains(input.Batchnumber.Trim()))
                    .WhereIF(input.GoodProductNum>0, u => u.GoodProductNum == input.GoodProductNum)
                    .WhereIF(input.RejectNum>0, u => u.RejectNum == input.RejectNum)
                    .WhereIF(input.WarrantyTime>0, u => u.WarrantyTime == input.WarrantyTime)
                    .Where(u => u.GoodProductNum > 0 || u.RejectNum > 0)

                    .Select<warehousebatchOutput>()
;
        if(input.ProduceTimeRange != null && input.ProduceTimeRange.Count >0)
        {
                DateTime? start= input.ProduceTimeRange[0]; 
                query = query.WhereIF(start.HasValue, u => u.ProduceTime > start);
                if (input.ProduceTimeRange.Count >1 && input.ProduceTimeRange[1].HasValue)
                {
                    var end = input.ProduceTimeRange[1].Value.AddDays(1);
                    query = query.Where(u => u.ProduceTime < end);
                }
        } 
        if(input.ExpirationTimeRange != null && input.ExpirationTimeRange.Count >0)
        {
                DateTime? start= input.ExpirationTimeRange[0]; 
                query = query.WhereIF(start.HasValue, u => u.ExpirationTime > start);
                if (input.ExpirationTimeRange.Count >1 && input.ExpirationTimeRange[1].HasValue)
                {
                    var end = input.ExpirationTimeRange[1].Value.AddDays(1);
                    query = query.Where(u => u.ExpirationTime < end);
                }
        } 
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加批次表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddwarehousebatchInput input)
    {
        var entity = input.Adapt<warehousebatch>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除批次表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletewarehousebatchInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新批次表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatewarehousebatchInput input)
    {
        var entity = input.Adapt<warehousebatch>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取批次表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<warehousebatch> Get([FromQuery] QueryByIdwarehousebatchInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取批次表列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<warehousebatchOutput>> List([FromQuery] warehousebatchInput input)
    {
        return await _rep.AsQueryable().Select<warehousebatchOutput>().ToListAsync();
    }





}

