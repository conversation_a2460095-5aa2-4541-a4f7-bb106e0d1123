﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.IO;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 科目设置服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SubjectService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Subject> _rep;
    public SubjectService(SqlSugarRepository<Subject> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询科目设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SubjectOutput>> Page(SubjectInput input)
    {
        var query= _rep.AsQueryable()
                    .Where(u => u.IsDelete == false)
                    .WhereIF(input.Id>0, u => u.Id == input.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SubjectCode), u => u.SubjectCode.Contains(input.SubjectCode.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SubjectName), u => u.SubjectName.Contains(input.SubjectName.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SubjectType), u => u.SubjectType.Equals(input.SubjectType.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SubjectDerict), u => u.SubjectDerict.Equals(input.SubjectDerict.Trim()))
                    .Select<SubjectOutput>()
                    .OrderBy(u => u.SubjectCode)
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加科目设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSubjectInput input)
    {
        var entity = input.Adapt<Subject>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 增加科目设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "AddList")]
    public async Task AddList(List<AddSubjectInput> input)
    {
        foreach (var item in input)
        {
            var entity = item.Adapt<Subject>();
            await _rep.InsertAsync(entity);
        }
    }

    /// <summary>
    /// 删除科目设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSubjectInput input)
    {
        var entity = input.Adapt<Subject>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新科目设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateSubjectInput input)
    {
        var entity = input.Adapt<Subject>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    ///// <summary>
    ///// 获取科目设置
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Subject> Get([FromQuery] QueryByIdSubjectInput input)
    //{
    //}

    /// <summary>
    /// 获取科目设置列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SubjectOutput>> List([FromQuery] SubjectInput input)
    {
        return await _rep.AsQueryable().Select<SubjectOutput>().ToListAsync();
    }

    /// <summary>
    /// 商品信息导入
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("import")]
    public async Task Import([FromForm] IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new Exception("文件不能为空");

        try
        {
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                IWorkbook workbook;
                if (file.FileName.EndsWith(".xlsx"))
                {
                    workbook = new XSSFWorkbook(stream);
                }
                else if (file.FileName.EndsWith(".xls"))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    throw new Exception("不支持的文件格式，请上传.xlsx或.xls文件");
                }

                ISheet sheet = workbook.GetSheetAt(0);

                for (int i = 0; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;

                    var input = new SubjectInput();
                    input.SubjectCode = row.GetCell(0).ToString();
                    input.SubjectName = row.GetCell(1).ToString();
                    input.SubjectType = row.GetCell(2).ToString();
                    input.SubjectDerict = row.GetCell(3).ToString();
                    var entity = input.Adapt<Subject>();
                    await _rep.InsertAsync(entity);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"导入失败：{ex.Message}");
        }
    }



}

