﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using NewLife;
using NPOI.XSSF.UserModel.Helpers;
using RazorEngine.Compilation.ImpromptuInterface;
using SqlSugar.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;

namespace Admin.NET.Application;
/// <summary> 
/// 入库单服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseInrecordService : IDynamicApiController, ITransient
{
    private readonly WarehouseStoreService _repws;
    private readonly SqlSugarRepository<WarehouseInrecord> _rep;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _repMX;
    private readonly SqlSugarRepository<WarehouseStore> _repSale;
    UserManager _userManager;
    public WarehouseInrecordService(
        SqlSugarRepository<WarehouseInrecord> rep,
        SqlSugarRepository<WarehouseInrecordMX> repMX,
        SqlSugarRepository<WarehouseStore> repSale,
        UserManager userManager)
    {
        _rep = rep;
        _repMX = repMX;
        _userManager = userManager;
        _repSale = repSale;
    }

    /// <summary>
    /// 分页查询入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseInrecordOutput>> Page(WarehouseInrecordInput input)
    {
        var checkStatus = !string.IsNullOrWhiteSpace(input.InhouseStatus) ? input.InhouseStatus.Split(',') : null;
        var query = _rep.AsQueryable()
            .LeftJoin<SysUser>((u, user) => u.CreateUserId == user.Id)
            .Where(u => u.TenantId == _userManager.TenantId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber.Contains(input.OrderNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.GoodsInfo.Contains(input.TradeName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PurchaseNumber), u => u.PurchaseNumber.Contains(input.PurchaseNumber.Trim()))
            .WhereIF(checkStatus != null, u => checkStatus.Contains(u.InhouseStatus.ToString(), true))
            .Where(u => u.IsDelete == false)
                .Select((u, user) => new WarehouseInrecordOutput
                {
                    Id = u.Id,
                    OrderNumber = u.OrderNumber,
                    PurchaseNumber = u.PurchaseNumber,
                    InhouseStatus = u.InhouseStatus,
                    InhouseType = u.InhouseType,
                    Remark = u.Remark,
                    SupplierId = u.SupplierId ?? 0,
                    Warehouseid = u.Warehouseid ?? 0,
                    CreateTime = u.CreateTime,
                    CreateUserName = user.RealName,
                    GoodsInfo = u.GoodsInfo,
                    TotalAmt = u.TotalAmt,
                    DiscountAmt = u.DiscountAmt,
                    ActualAmt = u.ActualAmt,

                });

        query = query.OrderBuilder(input);
        var list = await query.ToPagedListAsync(input.Page, input.PageSize);
        return list;
    }

    /// <summary>
    /// 增加入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> Add(AddWarehouseInrecordMx input)
    {
        var entity = input.addWarehouseInrecordInput.Adapt<WarehouseInrecord>();
        var listMx = input.listMx.Adapt<List<WarehouseInrecordMX>>();

        if (entity.Id > 0)
        {
            await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        else
        {
            var orderNumber = await App.GetRequiredService<PubOrderService>().GetNewOrder("RK");
            if (orderNumber.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }
            entity.OrderNumber = orderNumber;
            var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
            if (addSuccess)
            {
                listMx.ForEach(u => u.InrecordId = entity.Id);
            }
        }

        if (listMx != null)
        {
            await App.GetRequiredService<WarehouseInrecordMXService>().AddOrUpdate(listMx);
        }
        return entity.Id;
    }

    /// <summary>
    /// 根据采购单增加入库单和明细
    /// </summary>
    [HttpPost]
    public async Task AddByListPurchase(WarehousePurchase purchase)
    {
        var listPurchaseMx = await App.GetService<WarehousePurchaseMXService>().List(purchase.Id);
        //生成入库单和明细
        WarehouseInrecord warehouseInrecord = new WarehouseInrecord();

        //warehouseInrecord.GoodsInfo = string.Join(',', listPurchaseMx.Select(x => x.WarehousegoodsName));
        warehouseInrecord.GoodsInfo = purchase.GoodsInfo;
        warehouseInrecord.SupplierId = purchase.SupplierId;
        warehouseInrecord.TotalAmt = purchase.TotalAmt;
        warehouseInrecord.DiscountAmt = purchase.DiscountAmt;
        warehouseInrecord.ActualAmt = purchase.ActualAmt;

        var orderNumber = await App.GetService<PubOrderService>().GetNewOrder("RK");
        if (orderNumber.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCodeEnum.GY1001);
        }

        warehouseInrecord.OrderNumber = orderNumber;
        warehouseInrecord.PurchaseNumber = purchase.OrderNumber;
        warehouseInrecord.InhouseType = RcvTypeEnum.CaiGou;
        warehouseInrecord.InhouseStatus = RcvStatusEnum.NotReceived;
        warehouseInrecord.Remark = purchase.Remark;
        warehouseInrecord.Warehouseid = purchase.WarehouseId;
        _rep.AsInsertable(warehouseInrecord).ExecuteReturnBigIdentity();

        if (listPurchaseMx != null)
        {
            var listInrecordMx = new List<WarehouseInrecordMX>();
            foreach (var purchaseMx in listPurchaseMx)
            {
                var incordMx = new WarehouseInrecordMX();
                incordMx.InrecordId = warehouseInrecord.Id;
                incordMx.GoodsId = purchaseMx.GoodsId;
                incordMx.Rating = ShangPinPinJiEnum.LiangPin;
                incordMx.Unit = purchaseMx.Unit;
                incordMx.Unitprice = purchaseMx.PuchPrice ?? 0;
                incordMx.DocumentNum = purchaseMx.PuchQty;
                incordMx.TotalAmt = purchaseMx.PuchAmt;
                incordMx.RcvQty = 0;
                //incordMx.SupplierId = purchaseMx.SupplierId;
                listInrecordMx.Add(incordMx);
            }


            if (listInrecordMx.Count > 0)
            {
                await _repMX.AsInsertable(listInrecordMx).ExecuteCommandAsync();
            }
        }
    }

    /// <summary>
    /// 删除入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseInrecordInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseInrecordInput input)
    {
        var entity = input.Adapt<WarehouseInrecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 提交修改状态
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Submit")]
    public async Task Submit(List<long> listInputs)
    {
        var rpList = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();

        foreach (var rp in rpList)
        {
            if (rp.InhouseStatus != RcvStatusEnum.NotReceived)
            {
                throw Oops.Oh("只有待提交的单据能提交");
            }
            var listIncordMx = await _repMX.AsQueryable()
                .InnerJoin<WarehouseInrecord>((u, incord) => u.InrecordId == incord.Id && incord.IsDelete == false)
                .Where(u => u.IsDelete == false && u.InrecordId == rp.Id)
                    .Select((u, incord) => new WarehouseInrecordMXOutput
                    {
                        Id = u.Id,
                        InrecordId = u.InrecordId,
                        GoodsId = u.GoodsId,
                        RcvQty = u.RcvQty,
                        documentNum = u.DocumentNum,
                        warehouseId = incord.Warehouseid,
                        unique = u.Warehousegoods.isuniquecode,
                        ProductDate = u.ProductDate,
                        SupplierId = u.SupplierId,
                        Unit = u.Unit,
                        Shelflife = u.Shelflife,
                        Unitprice = u.Unitprice,

                    }).ToListAsync();

            var listWareHouseStore = await _repSale.AsQueryable()
                .Where(u => u.WarehouseId == rp.Warehouseid && listIncordMx.Select(x => x.GoodsId).Contains(u.TradeID))
                .Select(u => new WarehouseStoreOutput
                {
                    Id = u.Id,
                    WarehouseId = u.WarehouseId,
                    TradeID = u.TradeID,
                    IntransitNum = u.IntransitNum ?? 0
                }).ToListAsync();

            foreach (var item in listIncordMx)
            {
                var store = listWareHouseStore.Where(x => x.WarehouseId == item.warehouseId && x.TradeID == item.GoodsId).FirstOrDefault();
                if (store != null)
                {
                    store.IntransitNum += item.documentNum ?? 0;
                    var wareHouseStore = store.Adapt<WarehouseStore>();
                    await _repSale.AsUpdateable(wareHouseStore).UpdateColumns(x => new { x.IntransitNum }).ExecuteCommandAsync();
                }
                else
                {
                    var whs = new WarehouseStore
                    {
                        GoodProduct = 0,
                        Reject = 0,
                        TradeID = item.GoodsId,
                        WarehouseId = item.warehouseId,
                        IsUniqueCode = item.unique,
                        ProduceTime = item.ProductDate,
                        Quantity = item.documentNum,
                        SafetyStockLowNum = 0,
                        SafetyStockTallNum = 0,
                        Supplier = item.SupplierId.ToString(),
                        Unit = item.Unit,
                        Warranty = item.Shelflife,
                        PurchaseUnitPrice = item.Unitprice,
                        CurrentCost = item.Unitprice,
                        IntransitNum = item.documentNum
                    };
                    var entity = whs.Adapt<WarehouseStore>();
                    await _repSale.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
                    listWareHouseStore.Add(new WarehouseStoreOutput
                    {
                        Id = entity.Id,
                        WarehouseId = entity.WarehouseId,
                        TradeID = entity.TradeID,
                        IntransitNum = entity.IntransitNum ?? 0
                    });
                }
            }

            rp.InhouseStatus = RcvStatusEnum.Tobestored;
            await _rep.UpdateAsync(rp);
        }
    }


    /// <summary>
    /// 中止入库单
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Suspend")]
    public async Task Suspend(List<long> listInputs)
    {
        var rpList = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();

        foreach (var rp in rpList)
        {
            if (rp.InhouseStatus != RcvStatusEnum.PartiallyReceived)
            {
                throw Oops.Oh("状态为部分入库才能执行中止操作");
            }

            var listStore = _repSale.AsQueryable()
                .InnerJoin<WarehouseInrecordMX>((u, mx) => u.WarehouseId == rp.Warehouseid && u.TradeID == mx.GoodsId && mx.InrecordId == rp.Id && mx.DocumentNum != mx.RcvQty)
                .Select((u, mx) => new WarehouseStore
                {
                    Id = u.Id,
                    IntransitNum = u.IntransitNum - (mx.DocumentNum - mx.RcvQty),

                }).ToListAsync().Result;

            if (listStore != null && listStore.Count > 0)
            {
                await _repSale.AsUpdateable(listStore).UpdateColumns(x => new { x.IntransitNum }).ExecuteCommandAsync();
            }
            rp.InhouseStatus = RcvStatusEnum.Suspend;
            await _rep.UpdateAsync(rp);
        }
    }

    /// <summary>
    /// 获取入库单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>=
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehouseInrecord> Get([FromQuery] QueryByIdWarehouseInrecordInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 撤回
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Withdraw")]

    public async Task Withdraw(List<long> listInputs)
    {
        var rpList = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();
        foreach (var rp in rpList)
        {
            if (rp.InhouseStatus != RcvStatusEnum.Tobestored)
            {
                throw Oops.Oh("只有待入库的单据能撤回");
            }

            var listIncordMx = await _repMX.AsQueryable()
                .InnerJoin<WarehouseInrecord>((u, incord) => u.InrecordId == incord.Id && incord.IsDelete == false)
                .Where(u => u.IsDelete == false && u.InrecordId == rp.Id)
                    .Select((u, incord) => new WarehouseInrecordMXOutput
                    {
                        Id = u.Id,
                        InrecordId = u.InrecordId,
                        GoodsId = u.GoodsId,
                        RcvQty = u.RcvQty,
                        documentNum = u.DocumentNum,
                        warehouseId = incord.Warehouseid,
                        unique = u.Warehousegoods.isuniquecode,
                        ProductDate = u.ProductDate,
                        SupplierId = u.SupplierId,
                        Unit = u.Unit,
                        Shelflife = u.Shelflife,
                        Unitprice = u.Unitprice,

                    }).ToListAsync();

            var listWareHouseStore = await _repSale.AsQueryable()
                .Where(u => u.WarehouseId == rp.Warehouseid && listIncordMx.Select(x => x.GoodsId).Contains(u.TradeID))
                .Select(u => new WarehouseStoreOutput
                {
                    Id = u.Id,
                    WarehouseId = u.WarehouseId,
                    TradeID = u.TradeID,
                    IntransitNum = u.IntransitNum ?? 0
                }).ToListAsync();

            if (listWareHouseStore != null && listWareHouseStore.Count() > 0)
            {
                foreach (var item in listIncordMx)
                {
                    var store = listWareHouseStore.Where(x => x.WarehouseId == item.warehouseId && x.TradeID == item.GoodsId).First();
                    if (store != null)
                    {
                        store.IntransitNum -= item.documentNum ?? 0;
                    }
                }

                var list = listWareHouseStore.Adapt<List<WarehouseStore>>();
                await _repSale.AsUpdateable(list).UpdateColumns(x => new { x.IntransitNum }).ExecuteCommandAsync();
            }

            rp.InhouseStatus = RcvStatusEnum.NotReceived;
            await _rep.UpdateAsync(rp);
        }
    }

    /// <summary>
    /// 获取入库单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseInrecordOutput>> List([FromQuery] WarehouseInrecordInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseInrecordOutput>().ToListAsync();
    }





}

