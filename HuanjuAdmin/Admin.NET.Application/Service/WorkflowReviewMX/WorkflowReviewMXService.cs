﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;

namespace Admin.NET.Application;
/// <summary>
/// 流程审批明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WorkflowReviewMXService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WorkflowReviewMX> _rep;
    public WorkflowReviewMXService(SqlSugarRepository<WorkflowReviewMX> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询流程审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WorkflowReviewMXOutput>> Page(WorkflowReviewMXInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)

                    .Select(u => new WorkflowReviewMXOutput
                    {
                        Id = u.Id,
                        WorkflowId = u.WorkflowId,
                        NodeType = u.NodeType,
                        PositionId = u.PositionId,
                        SysPosName = u.SysPos.Name,
                        UserID = u.UserID,
                        SysUserRealName = u.SysUser.RealName,
                        StepNumber = u.StepNumber,
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加流程审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWorkflowReviewMXInput input)
    {
        var entity = input.Adapt<WorkflowReviewMX>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除流程审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWorkflowReviewMXInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新流程审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWorkflowReviewMXInput input)
    {
        var entity = input.Adapt<WorkflowReviewMX>();
        await _rep.AsUpdateable(entity).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取流程审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WorkflowReviewMX> Get([FromQuery] QueryByIdWorkflowReviewMXInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取流程审批明细列表
    /// </summary>
    /// <param name="workflowId"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WorkflowReviewMXOutput>> List(long workflowId)
    {
        var rs = await _rep.AsQueryable()
            .Where(x => x.WorkflowId == workflowId && x.IsDelete == false)
            .Select(u => new WorkflowReviewMXOutput
            {
                Id = u.Id,
                WorkflowId = u.WorkflowId,
                NodeType = u.NodeType,
                PositionId = u.PositionId,
                SysPosName = u.SysPos.Name,
                UserID = u.UserID,
                SysUserRealName = u.SysUser.RealName,
                StepNumber = u.StepNumber,
            }).OrderBy(x => x.StepNumber).ToListAsync();
        return rs;
    }

    /// <summary>
    /// 获取流程ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WorkflowReviewDropdown"), HttpGet]
    public async Task<dynamic> WorkflowReviewDropdown()
    {
        return await _rep.Context.Queryable<WorkflowReview>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.ProcessName,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取职位ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysPosDropdown"), HttpGet]
    public async Task<dynamic> SysPosDropdown()
    {
        return await _rep.Context.Queryable<SysPos>()
                .Where(x => x.IsDelete == false && x.Status == StatusEnum.Enable)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取用户ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysUserDropdown"), HttpGet]
    public async Task<dynamic> SysUserDropdown()
    {
        return await _rep.Context.Queryable<SysUser>()
                .Where(x => x.IsDelete == false && x.Status == StatusEnum.Enable)
                .Select(u => new
                {
                    Label = u.RealName,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

