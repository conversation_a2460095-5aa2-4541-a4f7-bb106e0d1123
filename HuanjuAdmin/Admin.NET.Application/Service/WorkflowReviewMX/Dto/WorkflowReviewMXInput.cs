﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 流程审批明细基础输入参数
    /// </summary>
    public class WorkflowReviewMXBaseInput
    {
        /// <summary>
        /// 流程ID
        /// </summary>
        public virtual long WorkflowId { get; set; }
        
        /// <summary>
        /// 节点类型
        /// </summary>
        public virtual StatusEnum NodeType { get; set; }
        
        /// <summary>
        /// 职位ID
        /// </summary>
        public virtual long? PositionId { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public virtual long? UserID { get; set; }
        
        /// <summary>
        /// 步骤编号
        /// </summary>
        public virtual int StepNumber { get; set; }
        
    }

    /// <summary>
    /// 流程审批明细分页查询输入参数
    /// </summary>
    public class WorkflowReviewMXInput : BasePageInput
    {
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 流程审批明细增加输入参数
    /// </summary>
    public class AddWorkflowReviewMXInput : WorkflowReviewMXBaseInput
    {
    }

    /// <summary>
    /// 流程审批明细删除输入参数
    /// </summary>
    public class DeleteWorkflowReviewMXInput : BaseIdInput
    {
    }

    /// <summary>
    /// 流程审批明细更新输入参数
    /// </summary>
    public class UpdateWorkflowReviewMXInput : WorkflowReviewMXBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 流程审批明细主键查询输入参数
    /// </summary>
    public class QueryByIdWorkflowReviewMXInput : DeleteWorkflowReviewMXInput
    {

    }
