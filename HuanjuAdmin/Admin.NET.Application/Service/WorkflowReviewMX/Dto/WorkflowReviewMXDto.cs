﻿namespace Admin.NET.Application;

    /// <summary>
    /// 流程审批明细输出参数
    /// </summary>
    public class WorkflowReviewMXDto
    {
        /// <summary>
        /// 流程ID
        /// </summary>
        public string WorkflowReviewProcessName { get; set; }
        
        /// <summary>
        /// 职位ID
        /// </summary>
        public string SysPosName { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public string SysUserRealName { get; set; }
        
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 流程ID
        /// </summary>
        public long WorkflowId { get; set; }
        
        /// <summary>
        /// 节点类型
        /// </summary>
        public StatusEnum NodeType { get; set; }
        
        /// <summary>
        /// 职位ID
        /// </summary>
        public long? PositionId { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public long? UserID { get; set; }
        
        /// <summary>
        /// 步骤编号
        /// </summary>
        public int StepNumber { get; set; }
        
    }
