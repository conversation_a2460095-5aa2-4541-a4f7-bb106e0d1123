﻿namespace Admin.NET.Application;

/// <summary>
/// 流程审批明细输出参数
/// </summary>
public class WorkflowReviewMXOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 流程ID
    /// </summary>
    public long WorkflowId { get; set; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public int NodeType { get; set; }

    /// <summary>
    /// 职位ID
    /// </summary>
    public long? PositionId { get; set; }

    /// <summary>
    /// 职位ID
    /// </summary>
    public string SysPosName { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public long? UserID { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string SysUserRealName { get; set; }

    /// <summary>
    /// 步骤编号
    /// </summary>
    public int StepNumber { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ApproveStatusEnum Status { get; set; }

}


