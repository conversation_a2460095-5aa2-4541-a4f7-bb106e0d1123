﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 收支明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class warerevenueService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<warerevenue> _rep;
    private readonly UserManager _userManager;
    public warerevenueService(
        UserManager userManager,
        SqlSugarRepository<warerevenue> rep)
    {
        _userManager = userManager;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询收支明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<warerevenueOutput>> Page(warerevenueInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.contactunits), u => input.contactunits.Contains(u.contactunits))
                    .LeftJoin<SysUser>((u, user) => u.handledby == user.Id)
                    .LeftJoin<TradingAccounts>((u, user, t) => u.Trading == t.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.handledbyName), (u, user) => input.handledbyName.Contains(user.RealName))
                    .WhereIF(input.revenueType != null, u => u.revenueType == input.revenueType)
                    .Select((u, user,t) => new warerevenueOutput
                    {
                        Id=u.Id,
                        revenueTime=u.revenueTime,
                        contactunits=u.contactunits,
                        ordernumber=u.ordernumber,
                        subject=u.subject,
                        levelsubject=u.levelsubject,
                        Incomeamount=u.Incomeamount,
                        expenditureamount=u.expenditureamount,
                        handledby=u.handledby,
                        notes=u.notes,
                        revenueType=u.revenueType,
                        handledbyName=user.RealName,
                        TradingName=t.Name
                    });
;
        if(input.revenueTimeRange != null && input.revenueTimeRange.Count >0)
        {
                DateTime? start= input.revenueTimeRange[0]; 
                query = query.WhereIF(start.HasValue, u => u.revenueTime > start);
                if (input.revenueTimeRange.Count >1 && input.revenueTimeRange[1].HasValue)
                {
                    var end = input.revenueTimeRange[1].Value.AddDays(1);
                    query = query.Where(u => u.revenueTime < end);
                }
        } 
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加收支明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddwarerevenueInput input)
    {
        var entity = input.Adapt<warerevenue>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除收支明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost]
    //[ApiDescriptionSettings(Name = "Delete")]
    //public async Task Delete(DeletewarerevenueInput input)
    //{
    //    await _rep.FakeDeleteAsync(entity);   //假删除
    //}

    /// <summary>
    /// 更新收支明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatewarerevenueInput input)
    {
        var entity = input.Adapt<warerevenue>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取收支明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<warerevenue> Get([FromQuery] QueryByIdwarerevenueInput input)
    //{
    //}

    /// <summary>
    /// 获取收支明细列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<warerevenueOutput>> List([FromQuery] warerevenueInput input)
    {
        return await _rep.AsQueryable().Select<warerevenueOutput>().ToListAsync();
    }





}

