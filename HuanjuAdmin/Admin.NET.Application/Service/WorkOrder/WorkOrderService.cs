﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using System;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 工单管理服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WorkOrderService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WorkOrder> _rep;
    public WorkOrderService(SqlSugarRepository<WorkOrder> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询工单管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WorkOrderOutput>> Page(WorkOrderInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(x => x.IsDelete == false)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber.Contains(input.OrderNumber.Trim()))
                    .WhereIF(input.Processor > 0, u => u.Processor == input.Processor)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Describe), u => u.Describe.Contains(input.Describe.Trim()))
                    .WhereIF(input.CustomId > 0, u => u.CustomId == input.CustomId)

                    .Select(u => new WorkOrderOutput
                    {
                        Id = u.Id,
                        OrderNumber = u.OrderNumber,
                        Processor = u.Processor,
                        SysUserRealName = u.SysUser.RealName,
                        ProcessTime = u.ProcessTime,
                        Describe = u.Describe,
                        Process = u.Process,
                        Management = u.Management,
                        Remark = u.Remark,
                        CustomId = u.CustomId,
                        PubcustomName = u.Pubcustom.Name,
                    })
;
        if (input.ProcessTimeRange != null && input.ProcessTimeRange.Count > 0)
        {
            DateTime? start = input.ProcessTimeRange[0];
            query = query.WhereIF(start.HasValue, u => u.ProcessTime > start);
            if (input.ProcessTimeRange.Count > 1 && input.ProcessTimeRange[1].HasValue)
            {
                var end = input.ProcessTimeRange[1].Value.AddDays(1);
                query = query.Where(u => u.ProcessTime < end);
            }
        }
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加工单管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWorkOrderInput input)
    {
        var entity = input.Adapt<WorkOrder>();

        if (entity != null)
        {
            var workOrder = await App.GetService<PubOrderService>().GetNewOrder("GD");
            if (workOrder.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }
            entity.OrderNumber = workOrder;
            if (entity.Processor != null)
            {
                entity.Process = WorkOrderStatusEnum.NotApproved;
            }
            if (entity.Management != 0)
            {
                entity.Process = WorkOrderStatusEnum.Approved;
                entity.ProcessTime = DateTime.Now;
            }
        }

        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除工单管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWorkOrderInput input)
    {
        var entity = input.Adapt<WorkOrder>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新工单管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWorkOrderInput input)
    {
        var entity = input.Adapt<WorkOrder>();
        if (entity.Process != WorkOrderStatusEnum.Completed)
        {
            if (entity.Processor != null)
            {
                entity.Process = WorkOrderStatusEnum.NotApproved;
            }
            if (entity.Management != 0)
            {
                entity.Process = WorkOrderStatusEnum.Approved;
                entity.ProcessTime = DateTime.Now;
            }
        }
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 批量完成工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Complete")]
    public async Task Complete(List<long> input)
    {
        if (input != null && input.Count > 0)
        {
            var listWorkOrder = await _rep.GetListAsync(u => input.Contains(u.Id) && u.IsDelete == false && u.Process == WorkOrderStatusEnum.Approved && u.Management != WorkOrderProcessEnum.Nothing) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
            var errorList = listWorkOrder.FindAll(x => !input.Exists(u => u == x.Id));
            if (errorList != null && errorList.Count > 0)
            {
                throw Oops.Oh("部分工单状态不正确，请刷新核对后重试");
            }

            await _rep.AsUpdateable().SetColumns(x => x.Process == WorkOrderStatusEnum.Completed).Where(u => input.Contains(u.Id)).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取工单管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WorkOrder> Get([FromQuery] QueryByIdWorkOrderInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取工单管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WorkOrderOutput>> List([FromQuery] WorkOrderInput input)
    {
        return await _rep.AsQueryable().Select<WorkOrderOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取处理人列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysUserDropdown"), HttpGet]
    public async Task<dynamic> SysUserDropdown()
    {
        return await _rep.Context.Queryable<SysUser>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.RealName,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取客户ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "PubcustomDropdown"), HttpGet]
    public async Task<dynamic> PubcustomDropdown()
    {
        return await _rep.Context.Queryable<Pubcustom>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

