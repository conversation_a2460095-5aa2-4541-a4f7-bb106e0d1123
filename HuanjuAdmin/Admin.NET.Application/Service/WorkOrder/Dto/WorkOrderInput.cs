﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 工单管理基础输入参数
    /// </summary>
    public class WorkOrderBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 工单编号
        /// </summary>
        public virtual string? OrderNumber { get; set; }
        
        /// <summary>
        /// 处理人
        /// </summary>
        public virtual long? Processor { get; set; }
        
        /// <summary>
        /// 处理时间
        /// </summary>
        public virtual DateTime? ProcessTime { get; set; }
        
        /// <summary>
        /// 问题描述
        /// </summary>
        public virtual string? Describe { get; set; }
        
        /// <summary>
        /// 进度
        /// </summary>
        public virtual WorkOrderStatusEnum Process { get; set; }
        
        /// <summary>
        /// 处理方式
        /// </summary>
        public virtual WorkOrderProcessEnum Management { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
        /// <summary>
        /// 客户ID
        /// </summary>
        public virtual long CustomId { get; set; }
        
    }

    /// <summary>
    /// 工单管理分页查询输入参数
    /// </summary>
    public class WorkOrderInput : BasePageInput
    {
        /// <summary>
        /// 工单编号
        /// </summary>
        public string? OrderNumber { get; set; }
        
        /// <summary>
        /// 处理人
        /// </summary>
        public long? Processor { get; set; }
        
        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessTime { get; set; }
        
        /// <summary>
         /// 处理时间范围
         /// </summary>
         public List<DateTime?> ProcessTimeRange { get; set; } 
        /// <summary>
        /// 问题描述
        /// </summary>
        public string? Describe { get; set; }
        
        /// <summary>
        /// 进度
        /// </summary>
        public WorkOrderStatusEnum Process { get; set; }
        
        /// <summary>
        /// 处理方式
        /// </summary>
        public WorkOrderProcessEnum Management { get; set; }
        
        /// <summary>
        /// 客户ID
        /// </summary>
        public long CustomId { get; set; }
        
    }

    /// <summary>
    /// 工单管理增加输入参数
    /// </summary>
    public class AddWorkOrderInput : WorkOrderBaseInput
    {
    }

    /// <summary>
    /// 工单管理删除输入参数
    /// </summary>
    public class DeleteWorkOrderInput : BaseIdInput
    {
    }

    /// <summary>
    /// 工单管理更新输入参数
    /// </summary>
    public class UpdateWorkOrderInput : WorkOrderBaseInput
    {
    }

    /// <summary>
    /// 工单管理主键查询输入参数
    /// </summary>
    public class QueryByIdWorkOrderInput : DeleteWorkOrderInput
    {

    }
