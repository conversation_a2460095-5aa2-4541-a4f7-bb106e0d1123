﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 工单管理输出参数
    /// </summary>
    public class WorkOrderOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 工单编号
       /// </summary>
       public string? OrderNumber { get; set; }
    
       /// <summary>
       /// 处理人
       /// </summary>
       public long? Processor { get; set; } 
       
       /// <summary>
       /// 处理人
       /// </summary>
       public string SysUserRealName { get; set; } 
    
       /// <summary>
       /// 处理时间
       /// </summary>
       public DateTime? ProcessTime { get; set; }
    
       /// <summary>
       /// 问题描述
       /// </summary>
       public string? Describe { get; set; }
    
       /// <summary>
       /// 进度
       /// </summary>
       public WorkOrderStatusEnum Process { get; set; }
    
       /// <summary>
       /// 处理方式
       /// </summary>
       public WorkOrderProcessEnum Management { get; set; }
    
       /// <summary>
       /// 备注
       /// </summary>
       public string? Remark { get; set; }
    
       /// <summary>
       /// 客户ID
       /// </summary>
       public long? CustomId { get; set; } 
       
       /// <summary>
       /// 客户ID
       /// </summary>
       public string PubcustomName { get; set; } 
    
    }
 

