﻿namespace Admin.NET.Application;

    /// <summary>
    /// 开票记录输出参数
    /// </summary>
    public class InvoicingRecordOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 单据号
       /// </summary>
       public string DocumentNumber { get; set; }
    
       /// <summary>
       /// 收款单ID
       /// </summary>
       public long ReceiptID { get; set; }
    
       /// <summary>
       /// 状态
       /// </summary>
       public int? Status { get; set; }
    
    }
 

