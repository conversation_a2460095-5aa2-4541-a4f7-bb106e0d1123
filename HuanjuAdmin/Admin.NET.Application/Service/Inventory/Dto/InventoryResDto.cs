﻿// MIT License
//
// Copyright (c) 2021-present <PERSON><PERSON><PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.Inventory.Dto;
public class InventoryResDto
{
    /// <summary>
    /// 商品名称
    /// </summary>
    public string TradeName { get; set; }

    /// <summary>
    /// 合同数量
    /// </summary>
    public int ContractNum{ get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public long? OutboundNum { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public int? OutOfStock { get; set; }
}
