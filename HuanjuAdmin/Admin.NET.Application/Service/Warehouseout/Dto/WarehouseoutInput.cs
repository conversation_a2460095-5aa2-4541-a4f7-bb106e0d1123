﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品出库基础输入参数
/// </summary>
public class WarehouseoutBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public long WarehouseId { get; set; }
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? OutOrder { get; set; }
    /// <summary>
    /// 出库类型
    /// </summary>
    public int? Outboundtype { get; set; }

    /// <summary>
    /// 出库时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public string? Auditstatus { get; set; }
    /// <summary>
    /// 客户Id
    /// </summary>
    public long CustomId { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }


    /// <summary>
    /// 物流单号
    /// </summary>
    public string? TrackingNumber { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 上级单号
    /// </summary>
    public string? SuperiorNum { get; set; }

    /// <summary>
    /// 商品信息
    /// </summary>
    public string? GoodsInfo { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public virtual decimal? TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public virtual decimal? DiscountAmt { get; set; }

    /// <summary>
    /// 实际金额
    /// </summary>
    public virtual decimal? ActualAmt { get; set; }
}

/// <summary>
/// 商品出库分页查询输入参数
/// </summary>
public class WarehouseoutInput : BasePageInput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public long WarehouseId { get; set; }
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? OutOrder { get; set; }
    /// <summary>
    /// 出库类型
    /// </summary>
    public int? Outboundtype { get; set; }

    /// <summary>
    /// 出库状态
    /// </summary>
    public string? outBoundStatus { get; set; }

    /// <summary>
    /// 出库时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public string? Auditstatus { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }


    /// <summary>
    /// 物流单号
    /// </summary>
    public string? TrackingNumber { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int? OutboundNum { get; set; }


    /// <summary>
    /// 商品名称
    /// </summary>
    public string? GoodsName { get; set; }

    /// <summary>
    /// 上级单号
    /// </summary>
    public string? SuperiorNum { get; set; }
}

/// <summary>
/// 商品出库增加输入参数
/// </summary>
public class AddWarehouseoutInput : WarehouseoutBaseInput
{
    public long Id { get; set; }

}
/// <summary>
/// 入库单增加输入参数
/// </summary>
public class AddWarehouseoutMx
{
    public AddWarehouseoutInput addWarehouseoutInput { get; set; }
    public List<AddWarehouseoutMXInput> listMx { get; set; }
}
/// <summary>
/// 商品出库删除输入参数
/// </summary>
public class DeleteWarehouseoutInput : BaseIdInput
{
}

/// <summary>
/// 商品出库更新输入参数
/// </summary>
public class UpdateWarehouseoutInput : WarehouseoutBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 商品出库主键查询输入参数
/// </summary>
public class QueryByIdWarehouseoutInput : DeleteWarehouseoutInput
{

}
