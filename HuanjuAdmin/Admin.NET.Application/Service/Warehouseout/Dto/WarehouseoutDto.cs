﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 商品出库输出参数
/// </summary>
public class WarehouseoutDto
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public long WarehouseId { get; set; }
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? OutOrder { get; set; }
    /// <summary>
    /// 出库类型
    /// </summary>
    public int? Outboundtype { get; set; }

    /// <summary>
    /// 出库时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public string? Auditstatus { get; set; }
    /// <summary>
    /// 客户Id
    /// </summary>
    public long CustomId { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }


    /// <summary>
    /// 物流单号
    /// </summary>
    public string? TrackingNumber { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

}
