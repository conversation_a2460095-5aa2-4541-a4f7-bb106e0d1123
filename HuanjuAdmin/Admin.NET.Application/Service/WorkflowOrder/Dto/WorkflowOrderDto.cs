﻿namespace Admin.NET.Application;

    /// <summary>
    /// 审批流程输出参数
    /// </summary>
    public class WorkflowOrderDto
    {
        /// <summary>
        /// 申请人ID
        /// </summary>
        public string SysUserRealName { get; set; }
        
        /// <summary>
        /// 流程ID
        /// </summary>
        public string WorkflowReviewProcessName { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        public string SysOrgName { get; set; }
        
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 申请人ID
        /// </summary>
        public long? UerId { get; set; }
        
        /// <summary>
        /// 审批单号
        /// </summary>
        public string ApprovalNumber { get; set; }
        
        /// <summary>
        /// 流程ID
        /// </summary>
        public long WorkflowId { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public RcvStatusEnum Status { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        public long OrgId { get; set; }
        
    }
