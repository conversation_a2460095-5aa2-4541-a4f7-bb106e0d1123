﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using FluentEmail.Core;
using Furion.FriendlyException;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 审批流程服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WorkflowOrderService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WorkflowOrder> _rep;
    UserManager _userManager;
    public WorkflowOrderService(SqlSugarRepository<WorkflowOrder> rep, UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询审批流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WorkflowOrderOutput>> Page(WorkflowOrderInput input)
    {
        //获取待我审批的明细
        var listWorkflowRecord = await App.GetService<WorkflowRecordService>().GetDaiWoSP(_userManager.UserId, ApproveStatusEnum.InProgress);
        List<long> listOrderIds = null;
        if (listWorkflowRecord != null && listWorkflowRecord.Count > 0)
        {
            listOrderIds = new List<long> { };
            listOrderIds.AddRange(listWorkflowRecord.Select(x => x.OrderId));
        }

        var query = _rep.AsQueryable()
                    .LeftJoin<SysPos>((u, p) => u.SysUser.PosId == p.Id)
                    .Where(u => u.UerId == _userManager.UserId)//我发起的审批记录
                    .WhereIF(listOrderIds != null && listOrderIds.Count > 0, u => listOrderIds.Contains(u.Id))//根据待我审批的明细获取审批记录
                    .WhereIF(input.UerId > 0, u => u.UerId == input.UerId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber), u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
                    .WhereIF(input.OrgId > 0, u => u.OrgId == input.OrgId)

                    .Select((u, p) => new WorkflowOrderOutput
                    {
                        Id = u.Id,
                        UerId = u.UerId,
                        Avatar = u.SysUser.Avatar,
                        CreateTime = u.CreateTime,
                        SysUserRealName = u.SysUser.RealName,
                        ApprovalNumber = u.ApprovalNumber,
                        WorkflowId = u.WorkflowId,
                        ProcessName = u.WorkflowReview.ProcessName,
                        ProcessAddress = u.WorkflowReview.ProcessAddress,
                        Status = u.Status,
                        OrgId = u.OrgId,
                        SysOrgName = u.SysOrg.Name,
                        SysPosName = p.Name,
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加审批流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWorkflowOrderInput input)
    {
        var entity = input.Adapt<WorkflowOrder>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 增加审批流程(返回ID)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<long> AddReturnID(AddWorkflowOrderInput input)
    {
        var entity = input.Adapt<WorkflowOrder>();
        await _rep.InsertReturnBigIdentityAsync(entity);
        return entity.Id;
    }

    /// <summary>
    /// 删除审批流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWorkflowOrderInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.DeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新审批流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWorkflowOrderInput input)
    {
        var entity = input.Adapt<WorkflowOrder>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取审批流程
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WorkflowOrder> Get([FromQuery] QueryByIdWorkflowOrderInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取审批流程列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WorkflowOrderOutput>> List([FromQuery] WorkflowOrderInput input)
    {
        return await _rep.AsQueryable().Select<WorkflowOrderOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取申请人ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysUserDropdown"), HttpGet]
    public async Task<dynamic> SysUserDropdown()
    {
        return await _rep.Context.Queryable<SysUser>()
            .Where(u => u.IsDelete == false)
                .Select(u => new
                {
                    Label = u.RealName,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取流程ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WorkflowReviewDropdown"), HttpGet]
    public async Task<dynamic> WorkflowReviewDropdown()
    {
        return await _rep.Context.Queryable<WorkflowReview>()
                .Where(u => u.IsDelete == false)
                .Select(u => new
                {
                    Label = u.ProcessName,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取部门ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysOrgDropdown"), HttpGet]
    public async Task<dynamic> SysOrgDropdown()
    {
        return await _rep.Context.Queryable<SysOrg>()
                .Where(u => u.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

