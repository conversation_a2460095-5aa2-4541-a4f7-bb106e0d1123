﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Http;
using Nest;
using NewLife;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;
using SqlSugar.Extensions;
using System;
using System.IO;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 工资条服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SalarySlipService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    UserManager _userManager;
    private readonly SqlSugarRepository<SalarySlip> _rep;
    public SalarySlipService(SqlSugarRepository<SalarySlip> rep, UserManager userManager, ISqlSugarClient db)
    {
        _rep = rep;
        _userManager = userManager;
        _db = db;
    }

    /// <summary>
    /// 分页查询工资条
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SalarySlipOutput>> Page(SalarySlipInput input)
    {
        if (input.SlipMonth == null)
        {
            throw Oops.Oh("请选择月份");
        }

        List<long> listUserID = null;
        if (!input.SysUserRealName.IsNullOrWhiteSpace())
        {
            var listUser = await App.GetService<SysUserService>().GetListByName(input.SysUserRealName);
            if (listUser == null) { return null; }
            listUserID = listUser.Select(x => x.Id).ToList();
        }

        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .WhereIF(listUserID != null, u => listUserID.Contains(u.UserID))
                    .WhereIF(input.SlipMonth != null, u => u.SlipMonth == input.SlipMonth.Value.Date.AddDays(1 - input.SlipMonth.Value.Day))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Grading), u => u.Grading.Contains(input.Grading.Trim()))

                    .Select(u => new SalarySlipOutput
                    {
                        Id = u.Id,
                        UserID = u.UserID,
                        SysOrgName = u.SysUser.SysOrg.Name,
                        SysPosName = u.SysUser.SysPos.Name,
                        SysUserRealName = u.SysUser.RealName,
                        SlipMonth = u.SlipMonth.ToString("yyyy-MM"),
                        ShouldDays = u.ShouldDays,
                        WorkDays = u.WorkDays,
                        ShouldMoney = u.ShouldMoney,
                        Grading = u.Grading,
                        KpiMoney = u.KpiMoney,
                        CommissionBase = u.CommissionBase,
                        CommissionRatio = u.CommissionRatio,
                        Commission = u.Commission,
                        WorkLateLeveEarly = u.WorkLateLeveEarly,
                        AttendanceTakeOff = u.AttendanceTakeOff,
                        Meals = u.Meals,
                        PhoneBill = u.PhoneBill,
                        TafficBill = u.TafficBill,
                        HouseBill = u.HouseBill,
                        Attendance = u.Attendance,
                        OrtheMoney = u.OrtheMoney,
                        SocialInsurance = u.SocialInsurance,
                        AccumulationFund = u.AccumulationFund,
                        Tax = u.Tax,
                        OrtherTakOff = u.OrtherTakOff,
                        ActualMoney = u.ActualMoney,
                        Remark = u.Remark,
                        Status = u.Status,
                    });

        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 根据用户ID查询工资条
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetListByUserID")]
    public async Task<SalarySlipOutput> GetListByUserID(string slipMonth)
    {
        if (slipMonth.IsNullOrEmpty())
            throw Oops.Oh("请选择月份");

        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.UserID == _userManager.UserId && u.Status == SalarySlipStatusEnum.Submit && u.SlipMonth == slipMonth.ToDateTime())
                    .Select(u => new SalarySlipOutput
                    {
                        Id = u.Id,
                        UserID = u.UserID,
                        SysOrgName = u.SysUser.SysOrg.Name,
                        SysPosName = u.SysUser.SysPos.Name,
                        SysUserRealName = u.SysUser.RealName,
                        SlipMonth = u.SlipMonth.ToString("yyyy-MM"),
                        ShouldDays = u.ShouldDays,
                        WorkDays = u.WorkDays,
                        ShouldMoney = u.ShouldMoney,
                        Grading = u.Grading,
                        KpiMoney = u.KpiMoney,
                        CommissionBase = u.CommissionBase,
                        CommissionRatio = u.CommissionRatio,
                        Commission = u.Commission,
                        WorkLateLeveEarly = u.WorkLateLeveEarly,
                        AttendanceTakeOff = u.AttendanceTakeOff,
                        Meals = u.Meals,
                        PhoneBill = u.PhoneBill,
                        TafficBill = u.TafficBill,
                        HouseBill = u.HouseBill,
                        Attendance = u.Attendance,
                        OrtheMoney = u.OrtheMoney,
                        SocialInsurance = u.SocialInsurance,
                        AccumulationFund = u.AccumulationFund,
                        Tax = u.Tax,
                        OrtherTakOff = u.OrtherTakOff,
                        ActualMoney = u.ActualMoney,
                        Remark = u.Remark,
                        Status = u.Status,
                    });

        return await query.FirstAsync();
    }

    /// <summary>
    /// 提交工资单
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Commit")]
    public async Task Commit(List<long> listSalarySlipId)
    {
        if (listSalarySlipId != null && listSalarySlipId.Count > 0)
        {
            var query = _rep.AsQueryable()
                .Where(u => u.IsDelete == false && listSalarySlipId.Contains(u.Id))
                .Select(u => new SalarySlipOutput
                {
                    Id = u.Id,
                    UserID = u.UserID,
                    SysOrgName = u.SysUser.SysOrg.Name,
                    SysPosName = u.SysUser.SysPos.Name,
                    SysUserRealName = u.SysUser.RealName,
                    SlipMonth = u.SlipMonth.ToString("yyyy-MM"),
                    ShouldDays = u.ShouldDays,
                    WorkDays = u.WorkDays,
                    ShouldMoney = u.ShouldMoney,
                    Grading = u.Grading,
                    KpiMoney = u.KpiMoney,
                    CommissionBase = u.CommissionBase,
                    CommissionRatio = u.CommissionRatio,
                    Commission = u.Commission,
                    WorkLateLeveEarly = u.WorkLateLeveEarly,
                    AttendanceTakeOff = u.AttendanceTakeOff,
                    Meals = u.Meals,
                    PhoneBill = u.PhoneBill,
                    TafficBill = u.TafficBill,
                    HouseBill = u.HouseBill,
                    Attendance = u.Attendance,
                    OrtheMoney = u.OrtheMoney,
                    SocialInsurance = u.SocialInsurance,
                    AccumulationFund = u.AccumulationFund,
                    Tax = u.Tax,
                    OrtherTakOff = u.OrtherTakOff,
                    ActualMoney = u.ActualMoney,
                    Remark = u.Remark,
                    Status = u.Status,
                });

            var listSalarySlip = await query.ToListAsync();
            var errorList = listSalarySlip.FindAll(u => u.Status != SalarySlipStatusEnum.NewAdd && u.Status != SalarySlipStatusEnum.Revoke);
            if (errorList != null && errorList.Count > 0)
            {
                throw Oops.Oh("以下同事的工资单无法提交，请刷新核对后重试：" + string.Join(",", errorList.Select(u => u.SysUserRealName)));
            }

            await _rep.AsUpdateable().SetColumns(x => x.Status == SalarySlipStatusEnum.Submit).Where(x => listSalarySlipId.Contains(x.Id)).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 撤回工资单
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Revocation")]
    public async Task Revocation(List<long> listSalarySlipId)
    {
        if (listSalarySlipId != null && listSalarySlipId.Count > 0)
        {
            var query = _rep.AsQueryable()
                .Where(u => u.IsDelete == false && listSalarySlipId.Contains(u.Id))
                .Select(u => new SalarySlipOutput
                {
                    Id = u.Id,
                    UserID = u.UserID,
                    SysOrgName = u.SysUser.SysOrg.Name,
                    SysPosName = u.SysUser.SysPos.Name,
                    SysUserRealName = u.SysUser.RealName,
                    SlipMonth = u.SlipMonth.ToString("yyyy-MM"),
                    ShouldDays = u.ShouldDays,
                    WorkDays = u.WorkDays,
                    ShouldMoney = u.ShouldMoney,
                    Grading = u.Grading,
                    KpiMoney = u.KpiMoney,
                    CommissionBase = u.CommissionBase,
                    CommissionRatio = u.CommissionRatio,
                    Commission = u.Commission,
                    WorkLateLeveEarly = u.WorkLateLeveEarly,
                    AttendanceTakeOff = u.AttendanceTakeOff,
                    Meals = u.Meals,
                    PhoneBill = u.PhoneBill,
                    TafficBill = u.TafficBill,
                    HouseBill = u.HouseBill,
                    Attendance = u.Attendance,
                    OrtheMoney = u.OrtheMoney,
                    SocialInsurance = u.SocialInsurance,
                    AccumulationFund = u.AccumulationFund,
                    Tax = u.Tax,
                    OrtherTakOff = u.OrtherTakOff,
                    ActualMoney = u.ActualMoney,
                    Remark = u.Remark,
                    Status = u.Status,
                });

            var listSalarySlip = await query.ToListAsync();
            var errorList = listSalarySlip.FindAll(u => u.Status != SalarySlipStatusEnum.Submit);
            if (errorList != null && errorList.Count > 0)
            {
                throw Oops.Oh("以下同事的工资单无法撤回，请刷新核对后重试：" + string.Join(",", errorList.Select(u => u.SysUserRealName)));
            }

            await _rep.AsUpdateable().SetColumns(x => x.Status == SalarySlipStatusEnum.Revoke).Where(x => listSalarySlipId.Contains(x.Id)).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 增加工资条
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSalarySlipInput input)
    {
        var entity = input.Adapt<SalarySlip>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除工资条
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSalarySlipInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新工资条
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateSalarySlipInput input)
    {
        var entity = input.Adapt<SalarySlip>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取工资条
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<SalarySlip> Get([FromQuery] QueryByIdSalarySlipInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取工资条列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SalarySlipOutput>> List([FromQuery] SalarySlipInput input)
    {
        return await _rep.AsQueryable().Select<SalarySlipOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取用户ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "SysUserDropdown"), HttpGet]
    public async Task<dynamic> SysUserDropdown()
    {
        return await _rep.Context.Queryable<SysUser>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.RealName,
                    Value = u.Id
                }
                ).ToListAsync();
    }


    /// <summary>
    /// 数据导入
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<string> Import(IFormFile file)
    {
        string ReturnValue = string.Empty;
        //定义一个bool类型的变量用来做验证
        bool flag = true;
        try
        {
            string fileExt = Path.GetExtension(file.FileName).ToLower();
            //定义一个集合一会儿将数据存储进来,全部一次丢到数据库中保存
            var Data = new List<SalarySlip>();
            MemoryStream ms = new MemoryStream();
            file.CopyTo(ms);
            ms.Seek(0, SeekOrigin.Begin);
            IWorkbook book;
            if (fileExt == ".xlsx")
            {
                book = new XSSFWorkbook(ms);
            }
            else if (fileExt == ".xls")
            {
                book = new HSSFWorkbook(ms);
            }
            else
            {
                book = null;
            }
            ISheet sheet = book.GetSheetAt(0);

            int CountRow = sheet.LastRowNum + 1;//获取总行数

            if (CountRow - 1 == 0)
            {
                return "Excel列表数据项为空!";

            }
            #region 循环验证
            for (int i = 1; i < CountRow; i++)
            {
                //获取第i行的数据
                var row = sheet.GetRow(i);
                if (row != null)
                {
                    //循环的验证单元格中的数据
                    for (int j = 0; j < 5; j++)
                    {
                        if (row.GetCell(j) == null || row.GetCell(j).ToString().Trim().Length == 0)
                        {
                            flag = false;
                            ReturnValue += $"第{i + 1}行,第{j + 1}列数据不能为空。";
                        }
                    }
                }
            }
            #endregion
            if (flag)
            {
                for (int i = 1; i < CountRow; i++)
                {
                    //实例化实体对象
                    SalarySlip commodity = new SalarySlip();
                    var row = sheet.GetRow(i);
                    //具体字段赋值
                    //if (row.GetCell(0) != null && row.GetCell(0).ToString().Trim().Length > 0)
                    //{
                    //    commodity.UserID = row.GetCell(0).ToString();
                    //}
                    //if (row.GetCell(1) != null && row.GetCell(1).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SysOrgName = row.GetCell(1).ToString();
                    //}
                    //if (row.GetCell(2) != null && row.GetCell(2).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SysPosName = row.GetCell(2).ToString();
                    //}
                    //if (row.GetCell(3) != null && row.GetCell(3).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SlipMonth = row.GetCell(3).ToString().ToString();
                    //}
                    if (row.GetCell(4) != null && row.GetCell(4).ToString().Trim().Length > 0)
                    {
                        commodity.Grading = row.GetCell(4).ToString();
                    }
                    else
                    {
                        commodity.Grading = "暂无";
                    }
                    Data.Add(commodity);
                }
                var data = _db.Insertable<SalarySlip>(Data).ExecuteCommand();
                ReturnValue = $"数据导入成功,共导入{CountRow - 1}条数据。";
            }

            if (!flag)
            {
                ReturnValue = "数据存在问题！" + ReturnValue;
            }
        }
        catch (Exception)
        {
            return "服务器异常";
        }

        return ReturnValue;
    }




}

