﻿using Admin.NET.Application.Service.OutboundRecord.Dto;
using SqlSugar;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 入库单明细输出参数
/// </summary>
public class WarehouseInrecordMXOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 入库单ID
    /// </summary>
    public long InrecordId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public string WarehousegoodsName { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? productCode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? brandName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? specsName { get; set; }

    /// <summary>
    /// 品级
    /// </summary>
    public ShangPinPinJiEnum Rating { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }


    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductDate { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public int? Shelflife { get; set; }

    /// <summary>
    /// 保质期单位
    /// </summary>
    public string? ShelflifeUnit { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? Expires { get; set; }

    /// <summary>   
    /// 过期预警（天）
    /// </summary>
    public int? ExpiryReminder { get; set; }

    /// <summary>
    /// 采购数量
    /// </summary>
    public int? PuchQty { get; set; }

    /// <summary>
    /// 入库数量
    /// </summary>
    public int? RcvQty { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public string PubSupplierName { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal Unitprice { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal? TotalAmt { get; set; }

    /// <summary>
    /// 是否唯一码
    /// </summary>
    public bool? unique { get; set; }

    /// <summary>
    /// 是否批次
    /// </summary>
    public bool? isbatch { get; set; }

    /// <summary>
    /// 单据数量
    /// </summary>
    public int? documentNum { get; set; }

    /// <summary>
    /// 是否良品
    /// </summary>
    public bool isproduct { get; set; }

    /// <summary>
    /// 是否良品
    /// </summary>
    public bool GoodProduct { get; set; }

    /// <summary>
    /// 产品类型描述（良品/次品）
    /// </summary>
    public string ProductTypeDesc => GoodProduct ? "良品" : "次品";

    /// <summary>
    /// 仓库ID
    /// </summary>
    public long? warehouseId { get; set; }

    /// <summary>
    /// 入库记录
    /// </summary>
    public List<OutInBoundOutput> listOutInBound { get; set; }
}


