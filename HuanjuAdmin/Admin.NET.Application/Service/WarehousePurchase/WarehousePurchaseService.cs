﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using FluentEmail.Core;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using NewLife;
using NewLife.Reflection;
using SqlSugar;
using System;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 商品采购服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehousePurchaseService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WarehousePurchase> _rep;
    private readonly SqlSugarRepository<WarehouseInrecord> _repInrecord;
    private readonly SqlSugarRepository<Paymentorder> _repPayment;
    UserManager _userManager;
    public WarehousePurchaseService(
        SqlSugarRepository<WarehousePurchase> rep,
        SqlSugarRepository<WarehouseInrecord> repInrecord,
        SqlSugarRepository<Paymentorder> repPayment,
        UserManager userManager)
    {
        _rep = rep;
        _repInrecord = repInrecord;
        _repPayment = repPayment;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询商品采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehousePurchaseOutput>> Page(WarehousePurchaseInput input)
    {
        var query = _rep.AsQueryable()
                    .LeftJoin<SysUser>((u, user) => u.CreateUserId == user.Id)
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber == input.OrderNumber)
                    .Select((u, user) => new WarehousePurchaseOutput
                    {
                        Id = u.Id,
                        OrderNumber = u.OrderNumber,
                        SupplierId = u.SupplierId,
                        SupplierName = u.supplier.Name,
                        WarehouseId = u.WarehouseId,
                        WarehouseName = u.Warehouse.Name,
                        DocumenntStatus = u.DocumenntStatus,
                        InhouseStatus = u.InhouseStatus,
                        GoodsInfo = u.GoodsInfo,
                        TotalAmt = u.TotalAmt,
                        DiscountAmt = u.DiscountAmt,
                        ActualAmt = u.ActualAmt,
                        Remark = u.Remark,
                        ApprNo = u.ApprNo,
                        CreateTime = u.CreateTime,
                        CreateUserName = user.RealName,
                    });
        ;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加商品采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehousePurchaseWithMX input)
    {
        var entity = input.addWarehousePurchaseInput.Adapt<WarehousePurchase>();
        var listMx = input.listMx.Adapt<List<WarehousePurchaseMX>>();

        entity.TotalAmt = entity.TotalAmt ?? 0;
        if (entity.Id > 0)
        {
            await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        else
        {
            var cgxh = await App.GetRequiredService<PubOrderService>().GetNewOrder("CG");
            if (cgxh.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }
            entity.OrderNumber = cgxh;
            var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
            if (addSuccess)
            {
                listMx.ForEach(u => u.PurchaseId = entity.Id);
            }
        }

        if (listMx != null)
        {
            await App.GetRequiredService<WarehousePurchaseMXService>().AddOrUpdate(listMx);
        }
    }

    /// <summary>
    /// 删除商品采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehousePurchaseInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehousePurchaseInput input)
    {
        var entity = input.Adapt<WarehousePurchase>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 提交商品采购
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Commit")]
    public async Task Commit(List<long> listPurchaseId)
    {
        if (listPurchaseId != null && listPurchaseId.Count > 0)
        {
            var listPurchase = await _rep.GetListAsync(u => u.IsDelete == false && listPurchaseId.Contains(u.Id)) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
            var errorList = listPurchase.FindAll(u => u.DocumenntStatus != OrderStatusEnum.Create && u.DocumenntStatus != OrderStatusEnum.Refunded);
            if (errorList != null && errorList.Count > 0)
            {
                throw Oops.Oh("以下采购单无法提交，请刷新核对后重试：" + string.Join(",", errorList.Select(u => u.OrderNumber)));
            }

            var incordMxService = App.GetService<WarehouseInrecordService>();
            var paymentorderService = App.GetService<PaymentOrderService>();
            foreach (var purchase in listPurchase)
            {
                var apprNo = "";
                //判断是否开启流程，如果开启则创建采购单审批流程，否则直接变为已审核并生成入库单。
                var rs = await App.GetService<WorkflowReviewService>().GetNewOrder("采购单审批", "CG");
                if (rs.Item1 < 0)
                {
                    throw Oops.Oh(rs.Item2);
                }

                if (rs.Item1 == 0)
                {
                    purchase.DocumenntStatus = OrderStatusEnum.Approved;
                    await incordMxService.AddByListPurchase(purchase);
                    await paymentorderService.AddByListPurchase(purchase);
                }
                else if (rs.Item1 == 1)
                {
                    apprNo = rs.Item2;
                    purchase.DocumenntStatus = OrderStatusEnum.NotApproved;
                }
            }

            await _rep.AsUpdateable(listPurchase).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 撤销商品采购
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Revocation")]
    public async Task Revocation(List<long> listPurchaseId)
    {
        if (listPurchaseId != null && listPurchaseId.Count > 0)
        {
            var listPurchase = await _rep.GetListAsync(u => listPurchaseId.Contains(u.Id)) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
            var errorList = listPurchase.FindAll(u => u.IsDelete == false && u.DocumenntStatus != OrderStatusEnum.Approved);
            if (errorList != null && errorList.Count > 0)
            {
                throw Oops.Oh("以下采购单无法撤回，请刷新核对后重试：" + string.Join(",", errorList.Select(u => u.OrderNumber)));
            }

            //付款单和入库单有一个有下一步动作(状态不是待提交)，就不允许撤回，提示某个单据状态不对。
            var purchaseNumber = listPurchase.Select(u => u.OrderNumber).ToList();
            var warewarehouseinrecord = _repInrecord.GetList(u => purchaseNumber.Contains(u.PurchaseNumber)).ToList();
            var inrecord = warewarehouseinrecord.FindAll(u => u.IsDelete == false && u.InhouseStatus != RcvStatusEnum.NotReceived);

            if (inrecord != null && inrecord.Count > 0)
            {
                throw Oops.Oh("以下入库单状态不对，请刷新核对后重试：" + string.Join(",", inrecord.Select(u => u.OrderNumber)));
            }
            else
            {
                _repInrecord.Delete(warewarehouseinrecord);
            }
            var paymentOrder = _repPayment.GetList(u => purchaseNumber.Contains(u.PurchaseNumber)).ToList();
            var payment = paymentOrder.FindAll(u => u.IsDelete == false && u.PaymentStatus != 0);
            if (payment != null && payment.Count > 0)
            {
                throw Oops.Oh("以下付款单状态不对，请刷新核对后重试：" + string.Join(",", payment.Select(u => u.PaymentNo)));
            }
            else
            {
                _repPayment.Delete(paymentOrder);
            }

            _rep.AsUpdateable().SetColumns(x => x.DocumenntStatus == OrderStatusEnum.Create).Where(x => listPurchaseId.Contains(x.Id)).ExecuteCommand();
        }
    }

    /// <summary>
    /// 获取商品采购
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehousePurchase> Get([FromQuery] QueryByIdWarehousePurchaseInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品采购列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehousePurchaseOutput>> List([FromQuery] WarehousePurchaseInput input)
    {
        return await _rep.AsQueryable().Select<WarehousePurchaseOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取仓库编号列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WarehouseDropdown"), HttpGet]
    public async Task<dynamic> WarehouseDropdown()
    {
        return await _rep.Context.Queryable<Warehouse>()
            .Where(x => x.TenantId == _userManager.TenantId)
                .Where(x => x.IsDelete == false && x.Status == 1)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

