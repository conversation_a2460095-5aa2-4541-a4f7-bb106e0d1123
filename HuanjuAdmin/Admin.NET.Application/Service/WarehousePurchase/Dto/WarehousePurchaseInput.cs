﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品采购基础输入参数
/// </summary>
public class WarehousePurchaseBaseInput
{
    /// <summary>
    /// 采购单号
    /// </summary>
    public virtual string OrderNumber { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    public virtual long? WarehouseId { get; set; }

    /// <summary>
    /// 单据状态
    /// </summary>
    public virtual OrderStatusEnum DocumenntStatus { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public virtual RcvStatusEnum InhouseStatus { get; set; }

    /// <summary>
    /// 商品信息
    /// </summary>
    public virtual string? GoodsInfo { get; set; }


    /// <summary>
    /// 总金额
    /// </summary>
    public virtual decimal? TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public virtual decimal? DiscountAmt { get; set; }

    /// <summary>
    /// 实际金额
    /// </summary>
    public virtual decimal? ActualAmt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 审批单号
    /// </summary>
    public virtual string ApprNo { get; set; }

}

/// <summary>
/// 商品采购分页查询输入参数
/// </summary>
public class WarehousePurchaseInput : BasePageInput
{
    /// <summary>
    /// 采购单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 单据状态
    /// </summary>
    public OrderStatusEnum DocumenntStatus { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public RcvStatusEnum InhouseStatus { get; set; }

}

/// <summary>
/// 商品采购增加输入参数
/// </summary>
public class AddWarehousePurchaseInput : WarehousePurchaseBaseInput
{
    public long? Id { get; set; }
}

public class AddWarehousePurchaseWithMX
{
    public AddWarehousePurchaseInput addWarehousePurchaseInput { get; set; }
    public List<AddWarehousePurchaseMXInput> listMx { get; set; }
}

/// <summary>
/// 商品采购删除输入参数
/// </summary>
public class DeleteWarehousePurchaseInput : BaseIdInput
{
}

/// <summary>
/// 商品采购更新输入参数
/// </summary>
public class UpdateWarehousePurchaseInput : WarehousePurchaseBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 商品采购主键查询输入参数
/// </summary>
public class QueryByIdWarehousePurchaseInput : DeleteWarehousePurchaseInput
{

}
