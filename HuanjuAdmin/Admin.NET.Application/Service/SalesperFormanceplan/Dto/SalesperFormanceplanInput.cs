﻿using Admin.NET.Application.Enum;
using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 履约计划基础输入参数
/// </summary>
public class SalesperFormanceplanBaseInput
{
    /// <summary>
    /// 时间
    /// </summary>
    public virtual DateTime? PlanTime { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public virtual SalesTypeEnmu Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public virtual SalesStatusEnmu Status { get; set; }

    /// <summary>
    /// 进度
    /// </summary>
    public virtual SalesScheduleEnmu Plan { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    public virtual string SalesOrder { get; set; }
    /// <summary>
    /// 计划关联id
    /// </summary>
    public virtual long? PlanId { get; set; }
}

/// <summary>
/// 履约计划分页查询输入参数
/// </summary>
public class SalesperFormanceplanInput : BasePageInput
{

    /// <summary>
    /// 销售单号
    /// </summary>
    public string SalesOrder { get; set; }
    /// <summary>
    /// 时间
    /// </summary>
    public DateTime? PlanTime { get; set; }

    /// <summary>
    /// 时间范围
    /// </summary>
    public List<DateTime?> PlanTimeRange { get; set; }
    /// <summary>
    /// 类型
    /// </summary>
    public SalesTypeEnmu Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SalesStatusEnmu Status { get; set; }

    /// <summary>
    /// 进度
    /// </summary>
    public SalesScheduleEnmu Plan { get; set; }

}

/// <summary>
/// 履约计划增加输入参数
/// </summary>
public class AddSalesperFormanceplanInput : SalesperFormanceplanBaseInput
{
    /// <summary>
    /// 时间
    /// </summary>
    [Required(ErrorMessage = "时间不能为空")]
    public override DateTime? PlanTime { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public override SalesTypeEnmu Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public override SalesStatusEnmu Status { get; set; }

    /// <summary>
    /// 进度
    /// </summary>
    [Required(ErrorMessage = "进度不能为空")]
    public override SalesScheduleEnmu Plan { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    [Required(ErrorMessage = "销售单号")]
    public override string SalesOrder { get; set; }

}

/// <summary>
/// 履约计划删除输入参数
/// </summary>
public class DeleteSalesperFormanceplanInput : BaseIdInput
{
}

/// <summary>
/// 履约计划更新输入参数
/// </summary>
public class UpdateSalesperFormanceplanInput : SalesperFormanceplanBaseInput
{
    public long Id { get; set; }
}

/// <summary>
/// 履约计划主键查询输入参数
/// </summary>
public class QueryByIdSalesperFormanceplanInput : DeleteSalesperFormanceplanInput
{

}
