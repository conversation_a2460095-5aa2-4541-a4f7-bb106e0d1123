﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 转换记录服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseconvertrecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Warehouseconvertrecord> _rep;
    UserManager _userManager;
    public WarehouseconvertrecordService(SqlSugarRepository<Warehouseconvertrecord> rep, UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询转换记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseconvertrecordOutput>> Page(WarehouseconvertrecordInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Commodity), u => (u.Commodity + "").Contains(input.Commodity.Trim()))
                    .WhereIF(input.Num > 0, u => u.Num == input.Num)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Notes), u => (u.Notes + "").Contains(input.Notes.Trim()))

                    .Select(u => new WarehouseconvertrecordOutput
                    {
                        Order = u.Order,
                        Commodity = u.Commodity,
                        Num = u.Num,
                        Notes = u.Notes,
                        TenantId = u.TenantId,
                        CreateTime = u.CreateTime,
                        UpdateTime = u.UpdateTime,
                        CreateUserName = u.Users.Account,
                        UpdateUserName = u.Users.Account,
                        Type = u.Type,
                        Warehouse = u.Warehouse,
                        UniqueCode = u.UniqueCode,
                        BatchNumber = u.BatchNumber
                    }

            )
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加转换记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseconvertrecordInput input)
    {
        var entity = input.Adapt<Warehouseconvertrecord>();
        //entity.CreateTime = DateTime.Now;
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除转换记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost]
    //[ApiDescriptionSettings(Name = "Delete")]
    //public async Task Delete(DeleteWarehouseconvertrecordInput input)
    //{
    //    await _rep.FakeDeleteAsync(entity);   //假删除
    //}

    /// <summary>
    /// 更新转换记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseconvertrecordInput input)
    {
        var entity = input.Adapt<Warehouseconvertrecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取转换记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Warehouseconvertrecord> Get([FromQuery] QueryByIdWarehouseconvertrecordInput input)
    //{
    //}

    /// <summary>
    /// 获取转换记录列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseconvertrecordOutput>> List([FromQuery] WarehouseconvertrecordInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseconvertrecordOutput>().ToListAsync();
    }





}

