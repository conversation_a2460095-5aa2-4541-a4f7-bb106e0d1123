﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 转换记录输出参数
/// </summary>
public class WarehouseconvertrecordOutput
{
    /// <summary>
    /// 序号
    /// </summary>
    [Required]
    [SugarColumn(ColumnDescription = "序号")]
    public int Order { get; set; }
    /// <summary>
    /// 商品
    /// </summary>
    [SugarColumn(ColumnDescription = "商品", Length = 50)]
    public string? Commodity { get; set; }
    /// <summary>
    /// 数量
    /// </summary>
    [Required]
    [SugarColumn(ColumnDescription = "数量")]
    public int Num { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 0)]
    public string? Notes { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    [SugarColumn(ColumnDescription = "创建人", Length = 50)]
    public string? CreateUserName { get; set; }
    /// <summary>
    /// 更新人
    /// </summary>
    [SugarColumn(ColumnDescription = "更新人", Length = 50)]
    public string? UpdateUserName { get; set; }
    /// <summary>
    /// 租户ID
    /// </summary>
    public long? TenantId { get; set; }
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    /// <summary>
    /// 转换类型
    /// </summary>
    public int? Type { get; set; }
    /// <summary>
    /// 仓库
    /// </summary>
    [SugarColumn(ColumnDescription = "仓库", Length = 100)]
    public string? Warehouse { get; set; }
    /// <summary>
    /// 唯一码
    /// </summary>
    [SugarColumn(ColumnDescription = "唯一码", Length = 100)]
    public  string? UniqueCode { get; set; }
    /// <summary>
    /// 批次号
    /// </summary>
    [SugarColumn(ColumnDescription = "批次号", Length = 100)]
    public  string? BatchNumber { get; set; }
}


