﻿namespace Admin.NET.Core;

/// <summary>
/// 系统租户表
/// </summary>
[SugarTable(null, "系统租户表")]
[SystemTable]
public class SysTenant : EntityBase
{
    /// <summary>
    /// 用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long UserId { get; set; }

    /// <summary>
    /// 机构Id
    /// </summary>
    [SugarColumn(ColumnDescription = "机构Id")]
    public long OrgId { get; set; }

    /// <summary>
    /// 主机
    /// </summary>
    [SugarColumn(ColumnDescription = "主机", Length = 128)]
    [MaxLength(128)]
    public string? Host { get; set; }

    /// <summary>
    /// 租户类型
    /// </summary>
    [SugarColumn(ColumnDescription = "租户类型")]
    public TenantTypeEnum TenantType { get; set; }

    /// <summary>
    /// 数据库类型
    /// </summary>
    [SugarColumn(ColumnDescription = "数据库类型")]
    public SqlSugar.DbType DbType { get; set; }

    /// <summary>
    /// 数据库连接
    /// </summary>
    [SugarColumn(ColumnDescription = "数据库连接", Length = 512)]
    [MaxLength(512)]
    public string? Connection { get; set; }

    /// <summary>
    /// 数据库标识
    /// </summary>
    [SugarColumn(ColumnDescription = "数据库标识", Length = 64)]
    [MaxLength(64)]
    public string? ConfigId { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int OrderNo { get; set; } = 100;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 128)]
    [MaxLength(128)]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 销售人ID
    /// </summary>
    [SugarColumn(ColumnDescription = "销售人ID")]
    public long? SaleUserId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "开始时间")]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 时长
    /// </summary>
    [SugarColumn(ColumnDescription = "时长")]
    public int? Duration { get; set; }

    /// <summary>
    /// 到期时间
    /// </summary>
    [SugarColumn(ColumnDescription = "到期时间")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 标识 0-新客 1-续费
    /// </summary>
    [SugarColumn(ColumnDescription = "标识 0-新客 1-续费")]
    public int? NewFlag { get; set; }

    /// <summary>
    /// 公钥-开票平台
    /// </summary>
    [SugarColumn(ColumnDescription = "公钥", Length = 64)]
    public string? PublicKey { get; set; }
    /// <summary>
    /// 私钥-开票平台
    /// </summary>
    [SugarColumn(ColumnDescription = "私钥", Length = 64)]
    public string? PrivateKey { get; set; }

    /// <summary>
    /// 租户来源
    /// </summary>
    [SugarColumn(ColumnDescription = "租户来源")]
    public TenantAppTypeEnum AppType { get; set; }
}